/*! tailwindcss v4.1.6 | MIT License | https://tailwindcss.com */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
      --angle: 0deg;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-red-950: oklch(25.8% .092 26.042);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-300: oklch(83.7% .128 66.29);
    --color-orange-400: oklch(75% .183 55.934);
    --color-orange-500: oklch(70.5% .213 47.604);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-900: oklch(40.8% .123 38.172);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-300: oklch(90.5% .182 98.111);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-yellow-900: oklch(42.1% .095 57.708);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-green-950: oklch(26.6% .065 152.934);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-indigo-500: oklch(58.5% .233 277.117);
    --color-indigo-600: oklch(51.1% .262 276.966);
    --color-indigo-700: oklch(45.7% .24 277.023);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-200: oklch(90.2% .063 306.703);
    --color-purple-500: oklch(62.7% .265 303.9);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-700: oklch(49.6% .265 301.924);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-rose-50: oklch(96.9% .015 12.422);
    --color-rose-400: oklch(71.2% .194 13.428);
    --color-rose-500: oklch(64.5% .246 16.439);
    --color-rose-950: oklch(27.1% .105 12.094);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-gray-950: oklch(13% .028 261.692);
    --color-zinc-50: oklch(98.5% 0 0);
    --color-zinc-100: oklch(96.7% .001 286.375);
    --color-zinc-200: oklch(92% .004 286.32);
    --color-zinc-300: oklch(87.1% .006 286.286);
    --color-zinc-400: oklch(70.5% .015 286.067);
    --color-zinc-500: oklch(55.2% .016 285.938);
    --color-zinc-600: oklch(44.2% .017 285.786);
    --color-zinc-700: oklch(37% .013 285.805);
    --color-zinc-800: oklch(27.4% .006 286.033);
    --color-zinc-900: oklch(21% .006 285.885);
    --color-zinc-950: oklch(14.1% .005 285.823);
    --color-neutral-200: oklch(92.2% 0 0);
    --color-neutral-300: oklch(87% 0 0);
    --color-neutral-400: oklch(70.8% 0 0);
    --color-neutral-600: oklch(43.9% 0 0);
    --color-neutral-700: oklch(37.1% 0 0);
    --color-neutral-900: oklch(20.5% 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --breakpoint-md: 48rem;
    --container-sm: 24rem;
    --container-lg: 32rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wide: .025em;
    --tracking-wider: .05em;
    --tracking-widest: .1em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-xs: .125rem;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-4xl: 2rem;
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-3xl: 64px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --squircle-border-radius-xs: 2px;
    --squircle-border-radius-sm: 4px;
    --squircle-border-radius-md: 6px;
    --squircle-border-radius-lg: 8px;
    --squircle-border-radius-xl: 12px;
    --squircle-border-radius-2xl: 16px;
    --squircle-border-radius-3xl: 24px;
  }
}

@layer base {
  *, :after, :before, ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-block: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]) {
    appearance: button;
  }

  ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  html {
    color-scheme: light dark;
  }

  * {
    border-color: hsl(var(--border));
  }

  html, body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  .using-mouse * {
    outline: none !important;
  }

  @font-face {
    font-family: manrope_1;
    src: url("/fonts/Manrope-Regular.woff2") format("woff2"), url("/fonts/Manrope-Regular.ttf") format("truetype");
    font-display: swap;
    font-weight: 400;
    font-style: normal;
  }

  @font-face {
    font-family: manrope_1;
    src: url("/fonts/Manrope-Medium.woff2") format("woff2");
    font-display: swap;
    font-weight: 500;
    font-style: normal;
  }

  @font-face {
    font-family: manrope_1;
    src: url("/fonts/Manrope-SemiBold.woff2") format("woff2");
    font-display: swap;
    font-weight: 600;
    font-style: normal;
  }

  @font-face {
    font-family: manrope_1;
    src: url("/fonts/Manrope-Bold.woff2") format("woff2");
    font-display: swap;
    font-weight: 700;
    font-style: normal;
  }

  .font-manrope_1 {
    font-family: manrope_1, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif !important;
  }

  :root {
    --sidebar: oklch(255.255 255 0);
    --sidebar-foreground: oklch(.145 0 0);
    --sidebar-primary: oklch(.205 0 0);
    --sidebar-primary-foreground: oklch(.985 0 0);
    --sidebar-accent: oklch(.97 0 0);
    --sidebar-accent-foreground: oklch(.205 0 0);
    --sidebar-border: oklch(.922 0 0);
    --sidebar-ring: oklch(.708 0 0);
  }

  .dark {
    --sidebar: oklch(.205 0 0);
    --sidebar-foreground: oklch(.985 0 0);
    --sidebar-primary: oklch(.488 .243 264.376);
    --sidebar-primary-foreground: oklch(.985 0 0);
    --sidebar-accent: oklch(.269 0 0);
    --sidebar-accent-foreground: oklch(.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(.439 0 0);
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .sticky {
    position: sticky;
  }

  .-inset-px {
    inset: -1px;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-\[-1000\%\] {
    inset: -1000%;
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .inset-y-\[0\.1rem\] {
    inset-block: .1rem;
  }

  .-top-1 {
    top: calc(var(--spacing) * -1);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-3\.5 {
    top: calc(var(--spacing) * 3.5);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-12 {
    top: calc(var(--spacing) * 12);
  }

  .top-\[0\.5rem\] {
    top: .5rem;
  }

  .top-\[2\.5rem\] {
    top: 2.5rem;
  }

  .top-\[13\.5vh\] {
    top: 13.5vh;
  }

  .top-\[28px\] {
    top: 28px;
  }

  .top-\[40vh\] {
    top: 40vh;
  }

  .-right-1 {
    right: calc(var(--spacing) * -1);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-0\.5 {
    right: calc(var(--spacing) * .5);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-\[1rem\] {
    right: 1rem;
  }

  .right-\[350px\] {
    right: 350px;
  }

  .-bottom-0\.5 {
    bottom: calc(var(--spacing) * -.5);
  }

  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }

  .bottom-2 {
    bottom: calc(var(--spacing) * 2);
  }

  .bottom-\[1rem\] {
    bottom: 1rem;
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-0\.5 {
    left: calc(var(--spacing) * .5);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-16 {
    left: calc(var(--spacing) * 16);
  }

  .left-\[18px\] {
    left: 18px;
  }

  .left-\[19px\] {
    left: 19px;
  }

  .left-\[24px\] {
    left: 24px;
  }

  .left-\[71px\] {
    left: 71px;
  }

  .left-\[72px\] {
    left: 72px;
  }

  .left-auto {
    left: auto;
  }

  .\!z-\[100000002\] {
    z-index: 100000002 !important;
  }

  .-z-10 {
    z-index: calc(10 * -1);
  }

  .z-0 {
    z-index: 0;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-60 {
    z-index: 60;
  }

  .z-\[-1\] {
    z-index: -1;
  }

  .z-\[1\] {
    z-index: 1;
  }

  .z-\[9999\] {
    z-index: 9999;
  }

  .z-\[99998\] {
    z-index: 99998;
  }

  .z-\[99999\] {
    z-index: 99999;
  }

  .z-\[100000\] {
    z-index: 100000;
  }

  .z-\[999999\] {
    z-index: 999999;
  }

  .z-\[9999999\] {
    z-index: 9999999;
  }

  .z-\[99999999\] {
    z-index: 99999999;
  }

  .z-\[100000001\] {
    z-index: 100000001;
  }

  .z-\[9999999999\], .z-\[100000000000\] {
    z-index: 2147483647;
  }

  .z-\[calc\(1000-var\(--toast-index\)\)\] {
    z-index: calc(1000 - var(--toast-index));
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .m-auto {
    margin: auto;
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }

  .mx-3\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .-mt-\[1rem\] {
    margin-top: -1rem;
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-auto {
    margin-top: auto;
  }

  .mr-0 {
    margin-right: calc(var(--spacing) * 0);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-16 {
    margin-right: calc(var(--spacing) * 16);
  }

  .mr-\[90vw\] {
    margin-right: 90vw;
  }

  .mr-\[350px\] {
    margin-right: 350px;
  }

  .mr-\[700px\] {
    margin-right: 700px;
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-12 {
    margin-left: calc(var(--spacing) * 12);
  }

  .ml-\[4\.5rem\] {
    margin-left: 4.5rem;
  }

  .ml-\[4rem\] {
    margin-left: 4rem;
  }

  .ml-\[284px\] {
    margin-left: 284px;
  }

  .ml-\[354px\] {
    margin-left: 354px;
  }

  .ml-\[356px\] {
    margin-left: 356px;
  }

  .ml-auto {
    margin-left: auto;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-4\.5 {
    width: calc(var(--spacing) * 4.5);
    height: calc(var(--spacing) * 4.5);
  }

  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-0 {
    height: calc(var(--spacing) * 0);
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-36 {
    height: calc(var(--spacing) * 36);
  }

  .h-\[1px\] {
    height: 1px;
  }

  .h-\[28px\] {
    height: 28px;
  }

  .h-\[40px\] {
    height: 40px;
  }

  .h-\[350px\] {
    height: 350px;
  }

  .h-auto {
    height: auto;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-screen {
    height: 100vh;
  }

  .h-svh {
    height: 100svh;
  }

  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-\[52px\] {
    min-height: 52px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .min-h-svh {
    min-height: 100svh;
  }

  .w-\(--radix-dropdown-menu-trigger-width\) {
    width: var(--radix-dropdown-menu-trigger-width);
  }

  .w-\(--sidebar-width\) {
    width: var(--sidebar-width);
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-18 {
    width: calc(var(--spacing) * 18);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-56 {
    width: calc(var(--spacing) * 56);
  }

  .w-\[1px\] {
    width: 1px;
  }

  .w-\[32px\] {
    width: 32px;
  }

  .w-\[36px\] {
    width: 36px;
  }

  .w-\[90vw\] {
    width: 90vw;
  }

  .w-\[220px\] {
    width: 220px;
  }

  .w-\[278px\] {
    width: 278px;
  }

  .w-\[284px\] {
    width: 284px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[350px\] {
    width: 350px;
  }

  .w-\[calc\(100vw-0px\)\] {
    width: 100vw;
  }

  .w-\[calc\(100vw-72px\)\] {
    width: calc(100vw - 72px);
  }

  .w-\[var\(--active-tab-width\)\] {
    width: var(--active-tab-width);
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-px {
    width: 1px;
  }

  .w-screen {
    width: 100vw;
  }

  .max-w-\(--skeleton-width\) {
    max-width: var(--skeleton-width);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-\[400px\] {
    max-width: 400px;
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-screen-md {
    max-width: var(--breakpoint-md);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }

  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }

  .min-w-56 {
    min-width: calc(var(--spacing) * 56);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-fit {
    min-width: fit-content;
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .-translate-x-1 {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-px {
    --tw-translate-x: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[var\(--active-tab-left\)\] {
    --tw-translate-x: var(--active-tab-left);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-px {
    --tw-translate-x: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1 {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[translate\:-50\%_-50\%\] {
    translate: -50% -50%;
  }

  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-110 {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .\[transform\:translateX\(var\(--toast-swipe-movement-x\)\)_translateY\(calc\(var\(--toast-swipe-movement-y\)\+calc\(min\(var\(--toast-index\)\,10\)\*-15px\)\)\)_scale\(calc\(max\(0\,1-\(var\(--toast-index\)\*0\.1\)\)\)\)\] {
    transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y)  + calc(min(var(--toast-index), 10) * -15px))) scale(calc(max(0, 1 - (var(--toast-index) * .1))));
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .transform-gpu {
    transform: translateZ(0) var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-\[spin_2s_linear_infinite\] {
    animation: 2s linear infinite spin;
  }

  .animate-in {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-shine {
    animation: 6s linear infinite shine;
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .touch-manipulation {
    touch-action: manipulation;
  }

  .resize {
    resize: both;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-nowrap {
    flex-wrap: nowrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  .gap-x-12 {
    column-gap: calc(var(--spacing) * 12);
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-gray-200 > :not(:last-child)) {
    border-color: var(--color-gray-200);
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .squircle-2xl {
    --squircle-border-radius: var(--squircle-border-radius-2xl);
    --squircle-border-top-left-radius: var(--squircle-border-radius-2xl);
    --squircle-border-top-right-radius: var(--squircle-border-radius-2xl);
    --squircle-border-bottom-right-radius: var(--squircle-border-radius-2xl);
    --squircle-border-bottom-left-radius: var(--squircle-border-radius-2xl);
  }

  @supports not (background: paint(squircle)) {
    .squircle-2xl {
      border-radius: var(--squircle-border-radius-2xl);
    }
  }

  .squircle-3xl {
    --squircle-border-radius: var(--squircle-border-radius-3xl);
    --squircle-border-top-left-radius: var(--squircle-border-radius-3xl);
    --squircle-border-top-right-radius: var(--squircle-border-radius-3xl);
    --squircle-border-bottom-right-radius: var(--squircle-border-radius-3xl);
    --squircle-border-bottom-left-radius: var(--squircle-border-radius-3xl);
  }

  @supports not (background: paint(squircle)) {
    .squircle-3xl {
      border-radius: var(--squircle-border-radius-3xl);
    }
  }

  .squircle-lg {
    --squircle-border-radius: var(--squircle-border-radius-lg);
    --squircle-border-top-left-radius: var(--squircle-border-radius-lg);
    --squircle-border-top-right-radius: var(--squircle-border-radius-lg);
    --squircle-border-bottom-right-radius: var(--squircle-border-radius-lg);
    --squircle-border-bottom-left-radius: var(--squircle-border-radius-lg);
  }

  @supports not (background: paint(squircle)) {
    .squircle-lg {
      border-radius: var(--squircle-border-radius-lg);
    }
  }

  .squircle-md {
    --squircle-border-radius: var(--squircle-border-radius-md);
    --squircle-border-top-left-radius: var(--squircle-border-radius-md);
    --squircle-border-top-right-radius: var(--squircle-border-radius-md);
    --squircle-border-bottom-right-radius: var(--squircle-border-radius-md);
    --squircle-border-bottom-left-radius: var(--squircle-border-radius-md);
  }

  @supports not (background: paint(squircle)) {
    .squircle-md {
      border-radius: var(--squircle-border-radius-md);
    }
  }

  .squircle-sm {
    --squircle-border-radius: var(--squircle-border-radius-sm);
    --squircle-border-top-left-radius: var(--squircle-border-radius-sm);
    --squircle-border-top-right-radius: var(--squircle-border-radius-sm);
    --squircle-border-bottom-right-radius: var(--squircle-border-radius-sm);
    --squircle-border-bottom-left-radius: var(--squircle-border-radius-sm);
  }

  @supports not (background: paint(squircle)) {
    .squircle-sm {
      border-radius: var(--squircle-border-radius-sm);
    }
  }

  .squircle-xl {
    --squircle-border-radius: var(--squircle-border-radius-xl);
    --squircle-border-top-left-radius: var(--squircle-border-radius-xl);
    --squircle-border-top-right-radius: var(--squircle-border-radius-xl);
    --squircle-border-bottom-right-radius: var(--squircle-border-radius-xl);
    --squircle-border-bottom-left-radius: var(--squircle-border-radius-xl);
  }

  @supports not (background: paint(squircle)) {
    .squircle-xl {
      border-radius: var(--squircle-border-radius-xl);
    }
  }

  .squircle-xs {
    --squircle-border-radius: var(--squircle-border-radius-xs);
    --squircle-border-top-left-radius: var(--squircle-border-radius-xs);
    --squircle-border-top-right-radius: var(--squircle-border-radius-xs);
    --squircle-border-bottom-right-radius: var(--squircle-border-radius-xs);
    --squircle-border-bottom-left-radius: var(--squircle-border-radius-xs);
  }

  @supports not (background: paint(squircle)) {
    .squircle-xs {
      border-radius: var(--squircle-border-radius-xs);
    }
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-\[10px\] {
    border-radius: 10px;
  }

  .rounded-\[inherit\] {
    border-radius: inherit;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-none {
    border-radius: 0;
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }

  .rounded-tl-4xl {
    border-top-left-radius: var(--radius-4xl);
  }

  .rounded-tl-xl {
    border-top-left-radius: var(--radius-xl);
  }

  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }

  .rounded-tr-4xl {
    border-top-right-radius: var(--radius-4xl);
  }

  .rounded-b-lg {
    border-bottom-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }

  .rounded-b-xl {
    border-bottom-right-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-1 {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-black\/5 {
    border-color: #0000000d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-black\/5 {
      border-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .border-black\/10 {
    border-color: #0000001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-black\/10 {
      border-color: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }

  .border-black\/20 {
    border-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-black\/20 {
      border-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-blue-400 {
    border-color: var(--color-blue-400);
  }

  .border-blue-500 {
    border-color: var(--color-blue-500);
  }

  .border-border {
    border-color: var(--border);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-500\/30 {
    border-color: #6a72824d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-500\/30 {
      border-color: color-mix(in oklab, var(--color-gray-500) 30%, transparent);
    }
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-500 {
    border-color: var(--color-green-500);
  }

  .border-orange-500\/50 {
    border-color: #fe6e0080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-orange-500\/50 {
      border-color: color-mix(in oklab, var(--color-orange-500) 50%, transparent);
    }
  }

  .border-primary\/10 {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/10 {
      border-color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .border-purple-200 {
    border-color: var(--color-purple-200);
  }

  .border-purple-500 {
    border-color: var(--color-purple-500);
  }

  .border-red-500\/20 {
    border-color: #fb2c3633;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-500\/20 {
      border-color: color-mix(in oklab, var(--color-red-500) 20%, transparent);
    }
  }

  .border-red-500\/30 {
    border-color: #fb2c364d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-500\/30 {
      border-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);
    }
  }

  .border-red-500\/50 {
    border-color: #fb2c3680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-red-500\/50 {
      border-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
    }
  }

  .border-rose-400 {
    border-color: var(--color-rose-400);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-white\/10 {
    border-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/10 {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .border-white\/20 {
    border-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/20 {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .border-yellow-500 {
    border-color: var(--color-yellow-500);
  }

  .border-zinc-100 {
    border-color: var(--color-zinc-100);
  }

  .border-zinc-200 {
    border-color: var(--color-zinc-200);
  }

  .border-zinc-200\/30 {
    border-color: #e4e4e74d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-zinc-200\/30 {
      border-color: color-mix(in oklab, var(--color-zinc-200) 30%, transparent);
    }
  }

  .border-zinc-200\/50 {
    border-color: #e4e4e780;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-zinc-200\/50 {
      border-color: color-mix(in oklab, var(--color-zinc-200) 50%, transparent);
    }
  }

  .border-zinc-200\/60 {
    border-color: #e4e4e799;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-zinc-200\/60 {
      border-color: color-mix(in oklab, var(--color-zinc-200) 60%, transparent);
    }
  }

  .border-zinc-300 {
    border-color: var(--color-zinc-300);
  }

  .border-zinc-300\/50 {
    border-color: #d4d4d880;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-zinc-300\/50 {
      border-color: color-mix(in oklab, var(--color-zinc-300) 50%, transparent);
    }
  }

  .border-r-\[\#212026\] {
    border-right-color: #212026;
  }

  .border-b-\[\#212026\] {
    border-bottom-color: #212026;
  }

  .bg-\[\#eeeeee\] {
    background-color: #eee;
  }

  .bg-\[\#f2f2f2\] {
    background-color: #f2f2f2;
  }

  .bg-\[\#f5f2ea\] {
    background-color: #f5f2ea;
  }

  .bg-\[\#f6f6f6\] {
    background-color: #f6f6f6;
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/5 {
    background-color: #0000000d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/5 {
      background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .bg-black\/10 {
    background-color: #0000001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/10 {
      background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }

  .bg-black\/20 {
    background-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/20 {
      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .bg-black\/30 {
    background-color: #0000004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/30 {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-500\/20 {
    background-color: #3080ff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/20 {
      background-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-border {
    background-color: var(--border);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-500\/20 {
    background-color: #00c75833;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-500\/20 {
      background-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);
    }
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }

  .bg-main-background {
    background-color: var(--main-background);
  }

  .bg-neutral-900 {
    background-color: var(--color-neutral-900);
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-orange-500\/20 {
    background-color: #fe6e0033;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-orange-500\/20 {
      background-color: color-mix(in oklab, var(--color-orange-500) 20%, transparent);
    }
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-500\/20 {
    background-color: #ac4bff33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/20 {
      background-color: color-mix(in oklab, var(--color-purple-500) 20%, transparent);
    }
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-rose-50 {
    background-color: var(--color-rose-50);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/5 {
    background-color: #ffffff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/5 {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .bg-white\/10 {
    background-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/10 {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-white\/50 {
    background-color: #ffffff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/50 {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-yellow-600 {
    background-color: var(--color-yellow-600);
  }

  .bg-zinc-50\/50 {
    background-color: #fafafa80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-zinc-50\/50 {
      background-color: color-mix(in oklab, var(--color-zinc-50) 50%, transparent);
    }
  }

  .bg-zinc-100 {
    background-color: var(--color-zinc-100);
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-l {
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-\[conic-gradient\(from_90deg_at_50\%_50\%\,\#4e4e4e_0\%\,\#d4d4d4_50\%\,\#414141_100\%\)\] {
    background-image: conic-gradient(from 90deg, #4e4e4e 0%, #d4d4d4 50%, #414141 100%);
  }

  .bg-\[linear-gradient\(110deg\,\#000000\,45\%\,\#303030\,55\%\,\#000000\)\] {
    background-image: linear-gradient(110deg, #000, 45%, #303030, 55%, #000);
  }

  .from-\[\#212026\] {
    --tw-gradient-from: #212026;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#c7c7c7\] {
    --tw-gradient-from: #c7c7c7;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#e8e8e8\] {
    --tw-gradient-from: #e8e8e8;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#f2f2f2\] {
    --tw-gradient-from: #f2f2f2;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#f8f8f8\] {
    --tw-gradient-from: #f8f8f8;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#f8f9fa\] {
    --tw-gradient-from: #f8f9fa;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-400 {
    --tw-gradient-from: var(--color-blue-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-800\/30 {
    --tw-gradient-from: #1e29394d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-gray-800\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--color-gray-800) 30%, transparent);
    }
  }

  .from-gray-950 {
    --tw-gradient-from: var(--color-gray-950);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-700 {
    --tw-gradient-from: var(--color-green-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-600 {
    --tw-gradient-from: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-800\/30 {
    --tw-gradient-from: #9f07124d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-red-800\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-800) 30%, transparent);
    }
  }

  .from-red-900\/20 {
    --tw-gradient-from: #82181a33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-red-900\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 20%, transparent);
    }
  }

  .from-red-900\/60 {
    --tw-gradient-from: #82181a99;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-red-900\/60 {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 60%, transparent);
    }
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white\/10 {
    --tw-gradient-from: #ffffff1a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .from-white\/20 {
    --tw-gradient-from: #fff3;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .from-white\/30 {
    --tw-gradient-from: #ffffff4d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  .from-zinc-100 {
    --tw-gradient-from: var(--color-zinc-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-\[\#212026\] {
    --tw-gradient-via: #212026;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[\#d9d9d9\] {
    --tw-gradient-via: #d9d9d9;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[\#e8e8e8\] {
    --tw-gradient-via: #e8e8e8;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[\#f4f4f4\] {
    --tw-gradient-via: #f4f4f4;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[\#f8f8f8\] {
    --tw-gradient-via: #f8f8f8;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[\#f8f9fa\] {
    --tw-gradient-via: #f8f9fa;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-indigo-500 {
    --tw-gradient-via: var(--color-indigo-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-neutral-300 {
    --tw-gradient-via: var(--color-neutral-300);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-transparent {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-zinc-200 {
    --tw-gradient-via: var(--color-zinc-200);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-\[\#29282e\] {
    --tw-gradient-to: #29282e;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#d8d8d8\] {
    --tw-gradient-to: #d8d8d8;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#e9ecef\] {
    --tw-gradient-to: #e9ecef;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#ececec\] {
    --tw-gradient-to: #ececec;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#eeeeee\] {
    --tw-gradient-to: #eee;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#f0f0f0\] {
    --tw-gradient-to: #f0f0f0;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-black\/10 {
    --tw-gradient-to: #0000001a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-black\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }

  .to-black\/20 {
    --tw-gradient-to: #0003;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-black\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .to-gray-900\/30 {
    --tw-gradient-to: #1018284d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-gray-900\/30 {
      --tw-gradient-to: color-mix(in oklab, var(--color-gray-900) 30%, transparent);
    }
  }

  .to-green-50\/40 {
    --tw-gradient-to: #f0fdf466;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-green-50\/40 {
      --tw-gradient-to: color-mix(in oklab, var(--color-green-50) 40%, transparent);
    }
  }

  .to-green-600 {
    --tw-gradient-to: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-neutral-400 {
    --tw-gradient-to: var(--color-neutral-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-500 {
    --tw-gradient-to: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-red-50\/40 {
    --tw-gradient-to: #fef2f266;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-red-50\/40 {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-50) 40%, transparent);
    }
  }

  .to-red-500 {
    --tw-gradient-to: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-red-900\/30 {
    --tw-gradient-to: #82181a4d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-red-900\/30 {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-900) 30%, transparent);
    }
  }

  .to-red-950\/20 {
    --tw-gradient-to: #46080933;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-red-950\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-950) 20%, transparent);
    }
  }

  .to-red-950\/60 {
    --tw-gradient-to: #46080999;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-red-950\/60 {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-950) 60%, transparent);
    }
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-zinc-200 {
    --tw-gradient-to: var(--color-zinc-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[84\%\] {
    --tw-gradient-to-position: 84%;
  }

  :where(.squircle-mask) {
    -webkit-mask-image: paint(squircle);
    -webkit-mask-image: paint(squircle);
    mask-image: paint(squircle);
    --squircle-border-radius: 0;
    --squircle-border-top-left-radius: 0;
    --squircle-border-top-right-radius: 0;
    --squircle-border-bottom-right-radius: 0;
    --squircle-border-bottom-left-radius: 0;
    --squircle-border-smoothing: .6;
    --squircle-border-top-left-smoothing: .6;
    --squircle-border-top-right-smoothing: .6;
    --squircle-border-bottom-right-smoothing: .6;
    --squircle-border-bottom-left-smoothing: .6;
    --squircle-border-width: 0;
    --squircle-border-color: unset;
    --squircle-background-color: unset;
    --squircle-mode: mask-image;
  }

  @supports not ((-webkit-mask-image: paint(squircle)) or (mask-image: paint(squircle))) {
    :where(.squircle-mask) {
      -webkit-mask-repeat: no-repeat;
      mask-repeat: no-repeat;
    }
  }

  .\[mask-image\:linear-gradient\(transparent\,transparent\)\,linear-gradient\(\#000\,\#000\)\] {
    -webkit-mask-image: linear-gradient(#0000, #0000), linear-gradient(#000, #000);
    mask-image: linear-gradient(#0000, #0000), linear-gradient(#000, #000);
  }

  .bg-\[length\:400\%_100\%\] {
    background-size: 400% 100%;
  }

  .bg-fixed {
    background-attachment: fixed;
  }

  .bg-clip-padding {
    background-clip: padding-box;
  }

  :where(.squircle) {
    background: paint(squircle);
    --squircle-border-radius: 0;
    --squircle-border-top-left-radius: 0;
    --squircle-border-top-right-radius: 0;
    --squircle-border-bottom-right-radius: 0;
    --squircle-border-bottom-left-radius: 0;
    --squircle-border-smoothing: .6;
    --squircle-border-top-left-smoothing: .6;
    --squircle-border-top-right-smoothing: .6;
    --squircle-border-bottom-right-smoothing: .6;
    --squircle-border-bottom-left-smoothing: .6;
    --squircle-border-width: 0;
    --squircle-border-color: unset;
    --squircle-background-color: unset;
    --squircle-mode: background;
  }

  @supports not (background: paint(squircle)) {
    :where(.squircle) {
      background-repeat: no-repeat;
    }
  }

  .\[mask-composite\:intersect\] {
    -webkit-mask-composite: source-in;
    -webkit-mask-composite: source-in;
    mask-composite: intersect;
  }

  .\[mask-clip\:padding-box\,border-box\] {
    -webkit-mask-clip: padding-box, border-box;
    mask-clip: padding-box, border-box;
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-green-500 {
    fill: var(--color-green-500);
  }

  .object-contain {
    object-fit: contain;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-px {
    padding: 1px;
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-12 {
    padding-top: calc(var(--spacing) * 12);
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-32 {
    padding-right: calc(var(--spacing) * 32);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pl-0 {
    padding-left: calc(var(--spacing) * 0);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .font-sans {
    font-family: var(--font-sans);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[8px\] {
    font-size: 8px;
  }

  .text-\[40pt\] {
    font-size: 40pt;
  }

  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }

  .leading-8 {
    --tw-leading: calc(var(--spacing) * 8);
    line-height: calc(var(--spacing) * 8);
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .whitespace-normal {
    white-space: normal;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-\[\#7e7b76\] {
    color: #7e7b76;
  }

  .text-black {
    color: var(--color-black);
  }

  .text-black\/10 {
    color: #0000001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-black\/10 {
      color: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }

  .text-black\/40 {
    color: #0006;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-black\/40 {
      color: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .text-black\/60 {
    color: #0009;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-black\/60 {
      color: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }

  .text-black\/70 {
    color: #000000b3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-black\/70 {
      color: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
  }

  .text-black\/80 {
    color: #000c;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-black\/80 {
      color: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }

  .text-black\/90 {
    color: #000000e6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-black\/90 {
      color: color-mix(in oklab, var(--color-black) 90%, transparent);
    }
  }

  .text-blue-100 {
    color: var(--color-blue-100);
  }

  .text-blue-400 {
    color: var(--color-blue-400);
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-blue-900 {
    color: var(--color-blue-900);
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-100 {
    color: var(--color-green-100);
  }

  .text-green-300 {
    color: var(--color-green-300);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-inherit {
    color: inherit;
  }

  .text-neutral-200 {
    color: var(--color-neutral-200);
  }

  .text-orange-400 {
    color: var(--color-orange-400);
  }

  .text-orange-700 {
    color: var(--color-orange-700);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-primary-invert\/90 {
    color: var(--primary-invert);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary-invert\/90 {
      color: color-mix(in oklab, var(--primary-invert) 90%, transparent);
    }
  }

  .text-primary-muted {
    color: var(--primary-muted);
  }

  .text-primary\/10 {
    color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary\/10 {
      color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-700 {
    color: var(--color-purple-700);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-purple-900 {
    color: var(--color-purple-900);
  }

  .text-red-200 {
    color: var(--color-red-200);
  }

  .text-red-300 {
    color: var(--color-red-300);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-rose-500 {
    color: var(--color-rose-500);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/70 {
    color: #ffffffb3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/70 {
      color: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }

  .text-white\/80 {
    color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/80 {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .text-zinc-50 {
    color: var(--color-zinc-50);
  }

  .text-zinc-400 {
    color: var(--color-zinc-400);
  }

  .text-zinc-500 {
    color: var(--color-zinc-500);
  }

  .text-zinc-600 {
    color: var(--color-zinc-600);
  }

  .text-zinc-700 {
    color: var(--color-zinc-700);
  }

  .text-zinc-800 {
    color: var(--color-zinc-800);
  }

  .text-zinc-900 {
    color: var(--color-zinc-900);
  }

  .text-zinc-950 {
    color: var(--color-zinc-950);
  }

  .uppercase {
    text-transform: uppercase;
  }

  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-10 {
    opacity: .1;
  }

  .opacity-20 {
    opacity: .2;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-60 {
    opacity: .6;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_-1px_0_1px_rgba\(0\,0\,0\,0\.8\)_inset\,0_0_0_1px_rgb\(9_9_11\)_inset\,0_0\.5px_0_1\.5px_\#71717a_inset\] {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #000c) inset, 0 0 0 1px var(--tw-shadow-color, #09090b) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #71717a) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_-1px_0_1px_rgba\(255\,255\,255\,0\.8\)_inset\,0_0_0_1px_rgb\(34\,197\,94\,0\.3\)_inset\,0_0\.5px_0_1\.5px_rgba\(34\,197\,94\,0\.2\)_inset\] {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #fffc) inset, 0 0 0 1px var(--tw-shadow-color, #22c55e4d) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #22c55e33) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_-1px_0_1px_rgba\(255\,255\,255\,0\.8\)_inset\,0_0_0_1px_rgb\(200_200_200\)_inset\,0_0\.5px_0_1\.5px_\#a1a1aa_inset\] {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #fffc) inset, 0 0 0 1px var(--tw-shadow-color, #c8c8c8) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #a1a1aa) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_-1px_0_1px_rgba\(255\,255\,255\,0\.8\)_inset\,0_0_0_1px_rgb\(220\,220\,220\)_inset\,0_0\.5px_0_1\.5px_\#ccc_inset\] {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #fffc) inset, 0 0 0 1px var(--tw-shadow-color, #dcdcdc) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #ccc) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_-1px_0_1px_rgba\(255\,255\,255\,0\.8\)_inset\,0_0_0_1px_rgb\(229_229_229\)_inset\,0_0\.5px_0_1\.5px_\#d4d4d8_inset\] {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #fffc) inset, 0 0 0 1px var(--tw-shadow-color, #e5e5e5) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #d4d4d8) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_-1px_0_1px_rgba\(255\,255\,255\,0\.8\)_inset\,0_0_0_1px_rgb\(239\,68\,68\,0\.3\)_inset\,0_0\.5px_0_1\.5px_rgba\(239\,68\,68\,0\.2\)_inset\] {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #fffc) inset, 0 0 0 1px var(--tw-shadow-color, #ef44444d) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #ef444433) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-black\/10 {
    --tw-shadow-color: #0000001a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-black\/10 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-black) 10%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-main-foreground\/70 {
    --tw-shadow-color: var(--main-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-main-foreground\/70 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--main-foreground) 70%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-black\/20 {
    --tw-ring-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-black\/20 {
      --tw-ring-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-\[0_2px_8px_rgba\(0\,0\,0\,0\.2\)\] {
    --tw-drop-shadow-size: drop-shadow(0 2px 8px var(--tw-drop-shadow-color, #0003));
    --tw-drop-shadow: var(--tw-drop-shadow-size);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-\[0_2px_8px_rgba\(0\,0\,0\,0\.4\)\] {
    --tw-drop-shadow-size: drop-shadow(0 2px 8px var(--tw-drop-shadow-color, #0006));
    --tw-drop-shadow: var(--tw-drop-shadow-size);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .\[filter\:url\(\#threshold\)_blur\(0\.6px\)\] {
    filter: url("#threshold") blur(.6px);
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-3xl {
    --tw-backdrop-blur: blur(var(--blur-3xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-\[32px\] {
    --tw-backdrop-blur: blur(32px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-saturate-\[180\%\] {
    --tw-backdrop-saturate: saturate(180%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[left\,right\,width\] {
    transition-property: left, right, width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[margin\,opacity\] {
    transition-property: margin, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\,padding\] {
    transition-property: width, height, padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .\[transition-property\:opacity\,transform\] {
    transition-property: opacity, transform;
  }

  .duration-100 {
    --tw-duration: .1s;
    transition-duration: .1s;
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .ease-\[cubic-bezier\(0\.22\,1\,0\.36\,1\)\] {
    --tw-ease: cubic-bezier(.22, 1, .36, 1);
    transition-timing-function: cubic-bezier(.22, 1, .36, 1);
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .will-change-transform {
    will-change: transform;
  }

  .fade-in-0 {
    --tw-enter-opacity: 0;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .zoom-in-95 {
    --tw-enter-scale: .95;
  }

  .group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *) {
    opacity: 1;
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }

    .group-hover\:text-black:is(:where(.group):hover *) {
      color: var(--color-black);
    }

    .group-hover\:text-white:is(:where(.group):hover *) {
      color: var(--color-white);
    }

    .group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *) {
      opacity: 1;
    }
  }

  .group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar="menu-action"]) *) {
    padding-right: calc(var(--spacing) * 8);
  }

  .group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible="icon"] *) {
    margin-top: calc(var(--spacing) * -8);
  }

  .group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible="icon"] *) {
    display: none;
  }

  .group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--spacing) * 8) !important;
    height: calc(var(--spacing) * 8) !important;
  }

  .group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible="icon"] *) {
    width: var(--sidebar-width-icon);
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4)));
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4))  + 2px);
  }

  .group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible="icon"] *) {
    overflow: hidden;
  }

  .group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 0) !important;
  }

  .group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 2) !important;
  }

  .group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible="icon"] *) {
    opacity: 0;
  }

  .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    right: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    left: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    width: calc(var(--spacing) * 0);
  }

  .group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side="left"] *) {
    right: calc(var(--spacing) * -4);
  }

  .group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side="left"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side="right"] *) {
    left: calc(var(--spacing) * 0);
  }

  .group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side="right"] *) {
    rotate: 180deg;
  }

  .group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side="right"] *) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .group-data-\[state\=open\]\/collapsible\:rotate-90:is(:where(.group\/collapsible)[data-state="open"] *) {
    rotate: 90deg;
  }

  .group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant="floating"] *) {
    border-radius: var(--radius-lg);
  }

  .group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant="floating"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant="floating"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
    top: calc(var(--spacing) * 1.5);
  }

  .peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
    top: calc(var(--spacing) * 2.5);
  }

  .peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
    top: calc(var(--spacing) * 1);
  }

  .selection\:bg-primary ::selection {
    background-color: var(--primary);
  }

  .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:text-primary-foreground ::selection {
    color: var(--primary-foreground);
  }

  .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .placeholder\:text-black\/60::placeholder {
    color: #0009;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .placeholder\:text-black\/60::placeholder {
      color: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }

  .placeholder\:text-red-700::placeholder {
    color: var(--color-red-700);
  }

  .before\:inset-x-0:before {
    content: var(--tw-content);
    inset-inline: calc(var(--spacing) * 0);
  }

  .before\:inset-y-1:before {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 1);
  }

  .before\:rounded-sm:before {
    content: var(--tw-content);
    border-radius: var(--radius-sm);
  }

  .before\:-outline-offset-1:before {
    content: var(--tw-content);
    outline-offset: calc(1px * -1);
  }

  .before\:outline-blue-800:before {
    content: var(--tw-content);
    outline-color: var(--color-blue-800);
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:-inset-2:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * -2);
  }

  .after\:inset-y-0:after {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 0);
  }

  .after\:bottom-full:after {
    content: var(--tw-content);
    bottom: 100%;
  }

  .after\:left-0:after {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .after\:left-1\/2:after {
    content: var(--tw-content);
    left: 50%;
  }

  .after\:h-\[calc\(var\(--gap\)\+1px\)\]:after {
    content: var(--tw-content);
    height: calc(var(--gap)  + 1px);
  }

  .after\:w-\[2px\]:after {
    content: var(--tw-content);
    width: 2px;
  }

  .after\:w-full:after {
    content: var(--tw-content);
    width: 100%;
  }

  .after\:content-\[\'\'\]:after {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible="offcanvas"] *):after {
    content: var(--tw-content);
    left: 100%;
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  @media (hover: hover) {
    .hover\:scale-95:hover {
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }

    .hover\:border-blue-300:hover {
      border-color: var(--color-blue-300);
    }

    .hover\:border-gray-500\/50:hover {
      border-color: #6a728280;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-gray-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-gray-500) 50%, transparent);
      }
    }

    .hover\:border-red-500\/30:hover {
      border-color: #fb2c364d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-red-500\/30:hover {
        border-color: color-mix(in oklab, var(--color-red-500) 30%, transparent);
      }
    }

    .hover\:border-red-500\/50:hover {
      border-color: #fb2c3680;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-red-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
      }
    }

    .hover\:bg-black\/5:hover {
      background-color: #0000000d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-black\/5:hover {
        background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
      }
    }

    .hover\:bg-black\/10:hover {
      background-color: #0000001a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-black\/10:hover {
        background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
      }
    }

    .hover\:bg-black\/20:hover {
      background-color: #0003;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-black\/20:hover {
        background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
      }
    }

    .hover\:bg-blue-500\/30:hover {
      background-color: #3080ff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-blue-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-blue-500) 30%, transparent);
      }
    }

    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }

    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }

    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }

    .hover\:bg-gray-700:hover {
      background-color: var(--color-gray-700);
    }

    .hover\:bg-green-500\/30:hover {
      background-color: #00c7584d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-green-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-green-500) 30%, transparent);
      }
    }

    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }

    .hover\:bg-indigo-700:hover {
      background-color: var(--color-indigo-700);
    }

    .hover\:bg-main-foreground\/40:hover {
      background-color: var(--main-foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-main-foreground\/40:hover {
        background-color: color-mix(in oklab, var(--main-foreground) 40%, transparent);
      }
    }

    .hover\:bg-main-foreground\/50:hover {
      background-color: var(--main-foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-main-foreground\/50:hover {
        background-color: color-mix(in oklab, var(--main-foreground) 50%, transparent);
      }
    }

    .hover\:bg-main-invert\/90:hover {
      background-color: var(--main-invert);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-main-invert\/90:hover {
        background-color: color-mix(in oklab, var(--main-invert) 90%, transparent);
      }
    }

    .hover\:bg-orange-500\/30:hover {
      background-color: #fe6e004d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-orange-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-orange-500) 30%, transparent);
      }
    }

    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }

    .hover\:bg-purple-500\/30:hover {
      background-color: #ac4bff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-purple-500\/30:hover {
        background-color: color-mix(in oklab, var(--color-purple-500) 30%, transparent);
      }
    }

    .hover\:bg-purple-700:hover {
      background-color: var(--color-purple-700);
    }

    .hover\:bg-red-600:hover {
      background-color: var(--color-red-600);
    }

    .hover\:bg-yellow-700:hover {
      background-color: var(--color-yellow-700);
    }

    .hover\:bg-zinc-50:hover {
      background-color: var(--color-zinc-50);
    }

    .hover\:bg-zinc-100\/70:hover {
      background-color: #f4f4f5b3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-zinc-100\/70:hover {
        background-color: color-mix(in oklab, var(--color-zinc-100) 70%, transparent);
      }
    }

    .hover\:text-black:hover {
      color: var(--color-black);
    }

    .hover\:text-black\/80:hover {
      color: #000c;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-black\/80:hover {
        color: color-mix(in oklab, var(--color-black) 80%, transparent);
      }
    }

    .hover\:text-black\/90:hover {
      color: #000000e6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-black\/90:hover {
        color: color-mix(in oklab, var(--color-black) 90%, transparent);
      }
    }

    .hover\:text-blue-200:hover {
      color: var(--color-blue-200);
    }

    .hover\:text-blue-300:hover {
      color: var(--color-blue-300);
    }

    .hover\:text-blue-900:hover {
      color: var(--color-blue-900);
    }

    .hover\:text-gray-100:hover {
      color: var(--color-gray-100);
    }

    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }

    .hover\:text-gray-900:hover {
      color: var(--color-gray-900);
    }

    .hover\:text-green-200:hover {
      color: var(--color-green-200);
    }

    .hover\:text-primary:hover {
      color: var(--primary);
    }

    .hover\:text-red-100:hover {
      color: var(--color-red-100);
    }

    .hover\:text-red-800:hover {
      color: var(--color-red-800);
    }

    .hover\:text-red-900:hover {
      color: var(--color-red-900);
    }

    .hover\:text-white\/40:hover {
      color: #fff6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-white\/40:hover {
        color: color-mix(in oklab, var(--color-white) 40%, transparent);
      }
    }

    .hover\:underline:hover {
      text-decoration-line: underline;
    }

    .hover\:opacity-100:hover {
      opacity: 1;
    }

    .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
      --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .hover\:duration-0:hover {
      --tw-duration: 0s;
      transition-duration: 0s;
    }
  }

  .focus\:border-blue-500:focus {
    border-color: var(--color-blue-500);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:relative:focus-visible {
    position: relative;
  }

  .focus-visible\:ring-1:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:before\:absolute:focus-visible:before {
    content: var(--tw-content);
    position: absolute;
  }

  .focus-visible\:before\:outline-2:focus-visible:before {
    content: var(--tw-content);
    outline-style: var(--tw-outline-style);
    outline-width: 2px;
  }

  .active\:scale-95:active {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:scale-100:disabled {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-40:disabled {
    opacity: .4;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .disabled\:opacity-60:disabled {
    opacity: .6;
  }

  :where([data-side="left"]) .in-data-\[side\=left\]\:cursor-w-resize {
    cursor: w-resize;
  }

  :where([data-side="right"]) .in-data-\[side\=right\]\:cursor-e-resize {
    cursor: e-resize;
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-disabled\:pointer-events-none[aria-disabled="true"] {
    pointer-events: none;
  }

  .aria-disabled\:opacity-50[aria-disabled="true"] {
    opacity: .5;
  }

  :is(:is(.\*\:data-rwp\:first\:\*\:data-rwp-highlight-wrapper\:rounded-s-md > *)[data-rwp]:first-child > *)[data-rwp-highlight-wrapper] {
    border-start-start-radius: var(--radius-md);
    border-end-start-radius: var(--radius-md);
  }

  :is(:is(.\*\:data-rwp\:last\:\*\:data-rwp-highlight-wrapper\:rounded-e-md > *)[data-rwp]:last-child > *)[data-rwp-highlight-wrapper] {
    border-start-end-radius: var(--radius-md);
    border-end-end-radius: var(--radius-md);
  }

  .data-\[active\=true\]\:font-medium[data-active="true"] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .data-\[closed\]\:animate-out[data-closed] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[closed\]\:duration-300[data-closed] {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .data-\[closed\]\:fade-out-0[data-closed] {
    --tw-exit-opacity: 0;
  }

  .data-\[closed\]\:slide-out-to-bottom[data-closed] {
    --tw-exit-translate-y: 100%;
  }

  .data-\[closed\]\:slide-out-to-left[data-closed] {
    --tw-exit-translate-x: -100%;
  }

  .data-\[closed\]\:slide-out-to-right[data-closed] {
    --tw-exit-translate-x: 100%;
  }

  .data-\[closed\]\:slide-out-to-top[data-closed] {
    --tw-exit-translate-y: -100%;
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[ending-style\]\:opacity-0[data-ending-style] {
    opacity: 0;
  }

  .data-\[expanded\]\:\[transform\:translateX\(var\(--toast-swipe-movement-x\)\)_translateY\(calc\(var\(--toast-offset-y\)\*-1\+calc\(var\(--toast-index\)\*var\(--gap\)\*-1\)\+var\(--toast-swipe-movement-y\)\)\)\][data-expanded] {
    transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-offset-y) * -1 + calc(var(--toast-index) * var(--gap) * -1)  + var(--toast-swipe-movement-y)));
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[limited\]\:opacity-0[data-limited] {
    opacity: 0;
  }

  .data-\[open\]\:animate-in[data-open] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[open\]\:duration-500[data-open] {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .data-\[open\]\:fade-in-0[data-open] {
    --tw-enter-opacity: 0;
  }

  .data-\[open\]\:slide-in-from-bottom[data-open] {
    --tw-enter-translate-y: 100%;
  }

  .data-\[open\]\:slide-in-from-left[data-open] {
    --tw-enter-translate-x: -100%;
  }

  .data-\[open\]\:slide-in-from-right[data-open] {
    --tw-enter-translate-x: 100%;
  }

  .data-\[open\]\:slide-in-from-top[data-open] {
    --tw-enter-translate-y: -100%;
  }

  .data-\[selected\]\:font-medium[data-selected] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .data-\[selected\]\:text-black\/90[data-selected] {
    color: #000000e6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\]\:text-black\/90[data-selected] {
      color: color-mix(in oklab, var(--color-black) 90%, transparent);
    }
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[starting-style\]\:\[transform\:translateY\(150\%\)\][data-starting-style] {
    transform: translateY(150%);
  }

  .data-\[state\=open\]\:opacity-100[data-state="open"] {
    opacity: 1;
  }

  .data-\[ending-style\]\:data-\[swipe-direction\=down\]\:\[transform\:translateY\(calc\(var\(--toast-swipe-movement-y\)\+150\%\)\)\][data-ending-style][data-swipe-direction="down"], .data-\[expanded\]\:data-\[ending-style\]\:data-\[swipe-direction\=down\]\:\[transform\:translateY\(calc\(var\(--toast-swipe-movement-y\)\+150\%\)\)\][data-expanded][data-ending-style][data-swipe-direction="down"] {
    transform: translateY(calc(var(--toast-swipe-movement-y)  + 150%));
  }

  .data-\[ending-style\]\:data-\[swipe-direction\=left\]\:\[transform\:translateX\(calc\(var\(--toast-swipe-movement-x\)-150\%\)\)_translateY\(var\(--offset-y\)\)\][data-ending-style][data-swipe-direction="left"], .data-\[expanded\]\:data-\[ending-style\]\:data-\[swipe-direction\=left\]\:\[transform\:translateX\(calc\(var\(--toast-swipe-movement-x\)-150\%\)\)_translateY\(var\(--offset-y\)\)\][data-expanded][data-ending-style][data-swipe-direction="left"] {
    transform: translateX(calc(var(--toast-swipe-movement-x)  - 150%)) translateY(var(--offset-y));
  }

  .data-\[ending-style\]\:data-\[swipe-direction\=right\]\:\[transform\:translateX\(calc\(var\(--toast-swipe-movement-x\)\+150\%\)\)_translateY\(var\(--offset-y\)\)\][data-ending-style][data-swipe-direction="right"], .data-\[expanded\]\:data-\[ending-style\]\:data-\[swipe-direction\=right\]\:\[transform\:translateX\(calc\(var\(--toast-swipe-movement-x\)\+150\%\)\)_translateY\(var\(--offset-y\)\)\][data-expanded][data-ending-style][data-swipe-direction="right"] {
    transform: translateX(calc(var(--toast-swipe-movement-x)  + 150%)) translateY(var(--offset-y));
  }

  .data-\[ending-style\]\:data-\[swipe-direction\=up\]\:\[transform\:translateY\(calc\(var\(--toast-swipe-movement-y\)-150\%\)\)\][data-ending-style][data-swipe-direction="up"], .data-\[expanded\]\:data-\[ending-style\]\:data-\[swipe-direction\=up\]\:\[transform\:translateY\(calc\(var\(--toast-swipe-movement-y\)-150\%\)\)\][data-expanded][data-ending-style][data-swipe-direction="up"] {
    transform: translateY(calc(var(--toast-swipe-movement-y)  - 150%));
  }

  @media (min-width: 40rem) {
    .sm\:right-\[2rem\] {
      right: 2rem;
    }

    .sm\:bottom-\[2rem\] {
      bottom: 2rem;
    }

    .sm\:mr-\[350px\] {
      margin-right: 350px;
    }

    .sm\:flex {
      display: flex;
    }

    .sm\:w-\[350px\] {
      width: 350px;
    }

    .sm\:max-w-sm {
      max-width: var(--container-sm);
    }

    .sm\:flex-row {
      flex-direction: row;
    }

    .sm\:items-center {
      align-items: center;
    }

    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:inset-y-1 {
      inset-block: calc(var(--spacing) * 1);
    }

    .md\:right-1 {
      right: calc(var(--spacing) * 1);
    }

    .md\:-bottom-0 {
      bottom: calc(var(--spacing) * 0);
    }

    .md\:left-1 {
      left: calc(var(--spacing) * 1);
    }

    .md\:left-\[19px\] {
      left: 19px;
    }

    .md\:mx-2 {
      margin-inline: calc(var(--spacing) * 2);
    }

    .md\:mt-2 {
      margin-top: calc(var(--spacing) * 2);
    }

    .md\:mr-2 {
      margin-right: calc(var(--spacing) * 2);
    }

    .md\:mr-20 {
      margin-right: calc(var(--spacing) * 20);
    }

    .md\:block {
      display: block;
    }

    .md\:flex {
      display: flex;
    }

    .md\:hidden {
      display: none;
    }

    .md\:h-24 {
      height: calc(var(--spacing) * 24);
    }

    .md\:min-h-\[64px\] {
      min-height: 64px;
    }

    .md\:max-w-\[30vw\] {
      max-width: 30vw;
    }

    .md\:max-w-\[400px\] {
      max-width: 400px;
    }

    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .md\:rounded-t-4xl {
      border-top-left-radius: var(--radius-4xl);
      border-top-right-radius: var(--radius-4xl);
    }

    .md\:p-4 {
      padding: calc(var(--spacing) * 4);
    }

    .md\:py-4 {
      padding-block: calc(var(--spacing) * 4);
    }

    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }

    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }

    .md\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }

    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }

    .md\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }

    .md\:opacity-0 {
      opacity: 0;
    }

    .md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin: calc(var(--spacing) * 2);
    }

    .md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin-left: calc(var(--spacing) * 0);
    }

    .md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant="inset"] ~ *) {
      border-radius: var(--radius-xl);
    }

    .md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant="inset"] ~ *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }

    .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant="inset"] ~ *):is(:where(.peer)[data-state="collapsed"] ~ *) {
      margin-left: calc(var(--spacing) * 2);
    }

    .md\:after\:hidden:after {
      content: var(--tw-content);
      display: none;
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .lg\:gap-x-16 {
      column-gap: calc(var(--spacing) * 16);
    }

    .lg\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }

    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }

    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }

    .lg\:text-\[6rem\] {
      font-size: 6rem;
    }
  }

  @media (min-width: 80rem) {
    .xl\:block {
      display: block;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:max-w-4xl {
      max-width: var(--container-4xl);
    }
  }

  .dark\:block:where(.dark, .dark *) {
    display: block;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:block:not(:where(.light, .light *)) {
      display: block;
    }
  }

  .dark\:hidden:where(.dark, .dark *) {
    display: none;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:hidden:not(:where(.light, .light *)) {
      display: none;
    }
  }

  :where(.dark\:divide-gray-700:where(.dark, .dark *) > :not(:last-child)) {
    border-color: var(--color-gray-700);
  }

  @media (prefers-color-scheme: dark) {
    :where(.dark\:divide-gray-700:not(:where(.light, .light *)) > :not(:last-child)) {
      border-color: var(--color-gray-700);
    }
  }

  .dark\:border-blue-800:where(.dark, .dark *) {
    border-color: var(--color-blue-800);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-blue-800:not(:where(.light, .light *)) {
      border-color: var(--color-blue-800);
    }
  }

  .dark\:border-gray-600:where(.dark, .dark *) {
    border-color: var(--color-gray-600);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-600:not(:where(.light, .light *)) {
      border-color: var(--color-gray-600);
    }
  }

  .dark\:border-gray-700:where(.dark, .dark *) {
    border-color: var(--color-gray-700);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-700:not(:where(.light, .light *)) {
      border-color: var(--color-gray-700);
    }
  }

  .dark\:border-green-800:where(.dark, .dark *) {
    border-color: var(--color-green-800);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-green-800:not(:where(.light, .light *)) {
      border-color: var(--color-green-800);
    }
  }

  .dark\:border-orange-400\/50:where(.dark, .dark *) {
    border-color: #ff8b1a80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-orange-400\/50:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-orange-400) 50%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-orange-400\/50:not(:where(.light, .light *)) {
      border-color: #ff8b1a80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-orange-400\/50:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-orange-400) 50%, transparent);
      }
    }
  }

  .dark\:border-red-400\/50:where(.dark, .dark *) {
    border-color: #ff656880;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-red-400\/50:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-red-400) 50%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-red-400\/50:not(:where(.light, .light *)) {
      border-color: #ff656880;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-red-400\/50:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-red-400) 50%, transparent);
      }
    }
  }

  .dark\:border-white\/5:where(.dark, .dark *) {
    border-color: #ffffff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-white\/5:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-white\/5:not(:where(.light, .light *)) {
      border-color: #ffffff0d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-white\/5:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }

  .dark\:border-white\/10:where(.dark, .dark *) {
    border-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-white\/10:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-white\/10:not(:where(.light, .light *)) {
      border-color: #ffffff1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-white\/10:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  .dark\:border-white\/20:where(.dark, .dark *) {
    border-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-white\/20:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-white\/20:not(:where(.light, .light *)) {
      border-color: #fff3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-white\/20:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  .dark\:border-yellow-800:where(.dark, .dark *) {
    border-color: var(--color-yellow-800);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-yellow-800:not(:where(.light, .light *)) {
      border-color: var(--color-yellow-800);
    }
  }

  .dark\:border-zinc-600:where(.dark, .dark *) {
    border-color: var(--color-zinc-600);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-zinc-600:not(:where(.light, .light *)) {
      border-color: var(--color-zinc-600);
    }
  }

  .dark\:border-zinc-600\/50:where(.dark, .dark *) {
    border-color: #52525c80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-zinc-600\/50:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-zinc-600) 50%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-zinc-600\/50:not(:where(.light, .light *)) {
      border-color: #52525c80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-zinc-600\/50:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-zinc-600) 50%, transparent);
      }
    }
  }

  .dark\:border-zinc-700:where(.dark, .dark *) {
    border-color: var(--color-zinc-700);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-zinc-700:not(:where(.light, .light *)) {
      border-color: var(--color-zinc-700);
    }
  }

  .dark\:border-zinc-700\/30:where(.dark, .dark *) {
    border-color: #3f3f464d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-zinc-700\/30:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-zinc-700) 30%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-zinc-700\/30:not(:where(.light, .light *)) {
      border-color: #3f3f464d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-zinc-700\/30:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-zinc-700) 30%, transparent);
      }
    }
  }

  .dark\:border-zinc-700\/50:where(.dark, .dark *) {
    border-color: #3f3f4680;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-zinc-700\/50:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-zinc-700) 50%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-zinc-700\/50:not(:where(.light, .light *)) {
      border-color: #3f3f4680;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-zinc-700\/50:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-zinc-700) 50%, transparent);
      }
    }
  }

  .dark\:border-zinc-700\/80:where(.dark, .dark *) {
    border-color: #3f3f46cc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:border-zinc-700\/80:where(.dark, .dark *) {
      border-color: color-mix(in oklab, var(--color-zinc-700) 80%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-zinc-700\/80:not(:where(.light, .light *)) {
      border-color: #3f3f46cc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-zinc-700\/80:not(:where(.light, .light *)) {
        border-color: color-mix(in oklab, var(--color-zinc-700) 80%, transparent);
      }
    }
  }

  .dark\:border-r-\[\#e9ecef\]:where(.dark, .dark *) {
    border-right-color: #e9ecef;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-r-\[\#e9ecef\]:not(:where(.light, .light *)) {
      border-right-color: #e9ecef;
    }
  }

  .dark\:border-b-\[\#e9ecef\]:where(.dark, .dark *) {
    border-bottom-color: #e9ecef;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-b-\[\#e9ecef\]:not(:where(.light, .light *)) {
      border-bottom-color: #e9ecef;
    }
  }

  .dark\:bg-\[\#0d0d0d\]:where(.dark, .dark *) {
    background-color: #0d0d0d;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[\#0d0d0d\]:not(:where(.light, .light *)) {
      background-color: #0d0d0d;
    }
  }

  .dark\:bg-\[\#0f0c05\]:where(.dark, .dark *) {
    background-color: #0f0c05;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[\#0f0c05\]:not(:where(.light, .light *)) {
      background-color: #0f0c05;
    }
  }

  .dark\:bg-\[\#29282e\]:where(.dark, .dark *) {
    background-color: #29282e;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[\#29282e\]:not(:where(.light, .light *)) {
      background-color: #29282e;
    }
  }

  .dark\:bg-\[\#101012\]:where(.dark, .dark *) {
    background-color: #101012;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[\#101012\]:not(:where(.light, .light *)) {
      background-color: #101012;
    }
  }

  .dark\:bg-\[\#171719\]:where(.dark, .dark *) {
    background-color: #171719;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[\#171719\]:not(:where(.light, .light *)) {
      background-color: #171719;
    }
  }

  .dark\:bg-\[\#212026\]:where(.dark, .dark *) {
    background-color: #212026;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[\#212026\]:not(:where(.light, .light *)) {
      background-color: #212026;
    }
  }

  .dark\:bg-\[\#272729\]:where(.dark, .dark *) {
    background-color: #272729;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[\#272729\]:not(:where(.light, .light *)) {
      background-color: #272729;
    }
  }

  .dark\:bg-black:where(.dark, .dark *) {
    background-color: var(--color-black);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-black:not(:where(.light, .light *)) {
      background-color: var(--color-black);
    }
  }

  .dark\:bg-black\/20:where(.dark, .dark *) {
    background-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-black\/20:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-black\/20:not(:where(.light, .light *)) {
      background-color: #0003;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-black\/20:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
      }
    }
  }

  .dark\:bg-blue-900\/20:where(.dark, .dark *) {
    background-color: #1c398e33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/20:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-900\/20:not(:where(.light, .light *)) {
      background-color: #1c398e33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-900\/20:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }
  }

  .dark\:bg-blue-900\/30:where(.dark, .dark *) {
    background-color: #1c398e4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-blue-900\/30:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-900\/30:not(:where(.light, .light *)) {
      background-color: #1c398e4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-900\/30:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
      }
    }
  }

  .dark\:bg-gray-700:where(.dark, .dark *) {
    background-color: var(--color-gray-700);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-700:not(:where(.light, .light *)) {
      background-color: var(--color-gray-700);
    }
  }

  .dark\:bg-gray-800:where(.dark, .dark *) {
    background-color: var(--color-gray-800);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-800:not(:where(.light, .light *)) {
      background-color: var(--color-gray-800);
    }
  }

  .dark\:bg-green-900:where(.dark, .dark *) {
    background-color: var(--color-green-900);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900:not(:where(.light, .light *)) {
      background-color: var(--color-green-900);
    }
  }

  .dark\:bg-green-900\/20:where(.dark, .dark *) {
    background-color: #0d542b33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-green-900\/20:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900\/20:not(:where(.light, .light *)) {
      background-color: #0d542b33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-900\/20:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }

  .dark\:bg-green-900\/30:where(.dark, .dark *) {
    background-color: #0d542b4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-green-900\/30:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900\/30:not(:where(.light, .light *)) {
      background-color: #0d542b4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-900\/30:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
      }
    }
  }

  .dark\:bg-orange-900\/30:where(.dark, .dark *) {
    background-color: #7e2a0c4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-orange-900\/30:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-orange-900) 30%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-orange-900\/30:not(:where(.light, .light *)) {
      background-color: #7e2a0c4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-orange-900\/30:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-orange-900) 30%, transparent);
      }
    }
  }

  .dark\:bg-red-900:where(.dark, .dark *) {
    background-color: var(--color-red-900);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-red-900:not(:where(.light, .light *)) {
      background-color: var(--color-red-900);
    }
  }

  .dark\:bg-red-900\/30:where(.dark, .dark *) {
    background-color: #82181a4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-red-900\/30:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-red-900\/30:not(:where(.light, .light *)) {
      background-color: #82181a4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-red-900\/30:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
      }
    }
  }

  .dark\:bg-rose-950:where(.dark, .dark *) {
    background-color: var(--color-rose-950);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-rose-950:not(:where(.light, .light *)) {
      background-color: var(--color-rose-950);
    }
  }

  .dark\:bg-transparent:where(.dark, .dark *) {
    background-color: #0000;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-transparent:not(:where(.light, .light *)) {
      background-color: #0000;
    }
  }

  .dark\:bg-white:where(.dark, .dark *) {
    background-color: var(--color-white);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-white:not(:where(.light, .light *)) {
      background-color: var(--color-white);
    }
  }

  .dark\:bg-white\/5:where(.dark, .dark *) {
    background-color: #ffffff0d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/5:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-white\/5:not(:where(.light, .light *)) {
      background-color: #ffffff0d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-white\/5:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }

  .dark\:bg-white\/10:where(.dark, .dark *) {
    background-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/10:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-white\/10:not(:where(.light, .light *)) {
      background-color: #ffffff1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-white\/10:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  .dark\:bg-white\/20:where(.dark, .dark *) {
    background-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/20:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-white\/20:not(:where(.light, .light *)) {
      background-color: #fff3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-white\/20:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  .dark\:bg-white\/30:where(.dark, .dark *) {
    background-color: #ffffff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-white\/30:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-white\/30:not(:where(.light, .light *)) {
      background-color: #ffffff4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-white\/30:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
      }
    }
  }

  .dark\:bg-yellow-900\/20:where(.dark, .dark *) {
    background-color: #733e0a33;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-900\/20:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-yellow-900\/20:not(:where(.light, .light *)) {
      background-color: #733e0a33;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-yellow-900\/20:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
      }
    }
  }

  .dark\:bg-yellow-900\/30:where(.dark, .dark *) {
    background-color: #733e0a4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-yellow-900\/30:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-yellow-900\/30:not(:where(.light, .light *)) {
      background-color: #733e0a4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-yellow-900\/30:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);
      }
    }
  }

  .dark\:bg-zinc-800:where(.dark, .dark *) {
    background-color: var(--color-zinc-800);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-zinc-800:not(:where(.light, .light *)) {
      background-color: var(--color-zinc-800);
    }
  }

  .dark\:bg-zinc-800\/50:where(.dark, .dark *) {
    background-color: #27272a80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-zinc-800\/50:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-zinc-800) 50%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-zinc-800\/50:not(:where(.light, .light *)) {
      background-color: #27272a80;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-zinc-800\/50:not(:where(.light, .light *)) {
        background-color: color-mix(in oklab, var(--color-zinc-800) 50%, transparent);
      }
    }
  }

  .dark\:bg-zinc-900:where(.dark, .dark *) {
    background-color: var(--color-zinc-900);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-zinc-900:not(:where(.light, .light *)) {
      background-color: var(--color-zinc-900);
    }
  }

  .dark\:bg-gradient-to-b:where(.dark, .dark *) {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gradient-to-b:not(:where(.light, .light *)) {
      --tw-gradient-position: to bottom in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
    }
  }

  .dark\:bg-gradient-to-r:where(.dark, .dark *) {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gradient-to-r:not(:where(.light, .light *)) {
      --tw-gradient-position: to right in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
    }
  }

  .dark\:bg-\[conic-gradient\(from_90deg_at_50\%_50\%\,\#c2c2c2_0\%\,\#505050_50\%\,\#bebebe_100\%\)\]:where(.dark, .dark *) {
    background-image: conic-gradient(from 90deg, #c2c2c2 0%, #505050 50%, #bebebe 100%);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[conic-gradient\(from_90deg_at_50\%_50\%\,\#c2c2c2_0\%\,\#505050_50\%\,\#bebebe_100\%\)\]:not(:where(.light, .light *)) {
      background-image: conic-gradient(from 90deg, #c2c2c2 0%, #505050 50%, #bebebe 100%);
    }
  }

  .dark\:bg-\[linear-gradient\(110deg\,\#000103\,45\%\,\#303030\,55\%\,\#000103\)\]:where(.dark, .dark *) {
    background-image: linear-gradient(110deg, #000103, 45%, #303030, 55%, #000103);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[linear-gradient\(110deg\,\#000103\,45\%\,\#303030\,55\%\,\#000103\)\]:not(:where(.light, .light *)) {
      background-image: linear-gradient(110deg, #000103, 45%, #303030, 55%, #000103);
    }
  }

  .dark\:from-\[\#0d0d0d\]:where(.dark, .dark *) {
    --tw-gradient-from: #0d0d0d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-\[\#0d0d0d\]:not(:where(.light, .light *)) {
      --tw-gradient-from: #0d0d0d;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:from-\[\#101012\]:where(.dark, .dark *) {
    --tw-gradient-from: #101012;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-\[\#101012\]:not(:where(.light, .light *)) {
      --tw-gradient-from: #101012;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:from-\[\#212026\]:where(.dark, .dark *) {
    --tw-gradient-from: #212026;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-\[\#212026\]:not(:where(.light, .light *)) {
      --tw-gradient-from: #212026;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:from-\[\#212123\]:where(.dark, .dark *) {
    --tw-gradient-from: #212123;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-\[\#212123\]:not(:where(.light, .light *)) {
      --tw-gradient-from: #212123;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:from-\[\#f8f9fa\]:where(.dark, .dark *) {
    --tw-gradient-from: #f8f9fa;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-\[\#f8f9fa\]:not(:where(.light, .light *)) {
      --tw-gradient-from: #f8f9fa;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:from-blue-500:where(.dark, .dark *) {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-blue-500:not(:where(.light, .light *)) {
      --tw-gradient-from: var(--color-blue-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:from-red-900\/20:where(.dark, .dark *) {
    --tw-gradient-from: #82181a33;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:from-red-900\/20:where(.dark, .dark *) {
      --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-red-900\/20:not(:where(.light, .light *)) {
      --tw-gradient-from: #82181a33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-red-900\/20:not(:where(.light, .light *)) {
        --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }
  }

  .dark\:from-transparent:where(.dark, .dark *) {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-transparent:not(:where(.light, .light *)) {
      --tw-gradient-from: transparent;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:via-\[\#18181a\]:where(.dark, .dark *) {
    --tw-gradient-via: #18181a;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:via-\[\#18181a\]:not(:where(.light, .light *)) {
      --tw-gradient-via: #18181a;
      --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
      --tw-gradient-stops: var(--tw-gradient-via-stops);
    }
  }

  .dark\:via-\[\#212026\]:where(.dark, .dark *) {
    --tw-gradient-via: #212026;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:via-\[\#212026\]:not(:where(.light, .light *)) {
      --tw-gradient-via: #212026;
      --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
      --tw-gradient-stops: var(--tw-gradient-via-stops);
    }
  }

  .dark\:via-\[\#f8f9fa\]:where(.dark, .dark *) {
    --tw-gradient-via: #f8f9fa;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:via-\[\#f8f9fa\]:not(:where(.light, .light *)) {
      --tw-gradient-via: #f8f9fa;
      --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
      --tw-gradient-stops: var(--tw-gradient-via-stops);
    }
  }

  .dark\:via-indigo-600:where(.dark, .dark *) {
    --tw-gradient-via: var(--color-indigo-600);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:via-indigo-600:not(:where(.light, .light *)) {
      --tw-gradient-via: var(--color-indigo-600);
      --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
      --tw-gradient-stops: var(--tw-gradient-via-stops);
    }
  }

  .dark\:via-neutral-700:where(.dark, .dark *) {
    --tw-gradient-via: var(--color-neutral-700);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:via-neutral-700:not(:where(.light, .light *)) {
      --tw-gradient-via: var(--color-neutral-700);
      --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
      --tw-gradient-stops: var(--tw-gradient-via-stops);
    }
  }

  .dark\:to-\[\#29282e\]:where(.dark, .dark *) {
    --tw-gradient-to: #29282e;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-\[\#29282e\]:not(:where(.light, .light *)) {
      --tw-gradient-to: #29282e;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:to-\[\#141416\]:where(.dark, .dark *) {
    --tw-gradient-to: #141416;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-\[\#141416\]:not(:where(.light, .light *)) {
      --tw-gradient-to: #141416;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:to-\[\#171719\]:where(.dark, .dark *) {
    --tw-gradient-to: #171719;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-\[\#171719\]:not(:where(.light, .light *)) {
      --tw-gradient-to: #171719;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:to-\[\#e9ecef\]:where(.dark, .dark *) {
    --tw-gradient-to: #e9ecef;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-\[\#e9ecef\]:not(:where(.light, .light *)) {
      --tw-gradient-to: #e9ecef;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:to-green-950\/40:where(.dark, .dark *) {
    --tw-gradient-to: #032e1566;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-green-950\/40:where(.dark, .dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-green-950) 40%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-green-950\/40:not(:where(.light, .light *)) {
      --tw-gradient-to: #032e1566;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-green-950\/40:not(:where(.light, .light *)) {
        --tw-gradient-to: color-mix(in oklab, var(--color-green-950) 40%, transparent);
      }
    }
  }

  .dark\:to-neutral-600:where(.dark, .dark *) {
    --tw-gradient-to: var(--color-neutral-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-neutral-600:not(:where(.light, .light *)) {
      --tw-gradient-to: var(--color-neutral-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:to-purple-600:where(.dark, .dark *) {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-purple-600:not(:where(.light, .light *)) {
      --tw-gradient-to: var(--color-purple-600);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  .dark\:to-red-950\/20:where(.dark, .dark *) {
    --tw-gradient-to: #46080933;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-red-950\/20:where(.dark, .dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-950) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-red-950\/20:not(:where(.light, .light *)) {
      --tw-gradient-to: #46080933;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-red-950\/20:not(:where(.light, .light *)) {
        --tw-gradient-to: color-mix(in oklab, var(--color-red-950) 20%, transparent);
      }
    }
  }

  .dark\:to-red-950\/40:where(.dark, .dark *) {
    --tw-gradient-to: #46080966;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:to-red-950\/40:where(.dark, .dark *) {
      --tw-gradient-to: color-mix(in oklab, var(--color-red-950) 40%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-red-950\/40:not(:where(.light, .light *)) {
      --tw-gradient-to: #46080966;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-red-950\/40:not(:where(.light, .light *)) {
        --tw-gradient-to: color-mix(in oklab, var(--color-red-950) 40%, transparent);
      }
    }
  }

  .dark\:text-black\/80:where(.dark, .dark *) {
    color: #000c;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-black\/80:where(.dark, .dark *) {
      color: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-black\/80:not(:where(.light, .light *)) {
      color: #000c;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-black\/80:not(:where(.light, .light *)) {
        color: color-mix(in oklab, var(--color-black) 80%, transparent);
      }
    }
  }

  .dark\:text-blue-200:where(.dark, .dark *) {
    color: var(--color-blue-200);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-200:not(:where(.light, .light *)) {
      color: var(--color-blue-200);
    }
  }

  .dark\:text-blue-300:where(.dark, .dark *) {
    color: var(--color-blue-300);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-300:not(:where(.light, .light *)) {
      color: var(--color-blue-300);
    }
  }

  .dark\:text-blue-400:where(.dark, .dark *) {
    color: var(--color-blue-400);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-400:not(:where(.light, .light *)) {
      color: var(--color-blue-400);
    }
  }

  .dark\:text-gray-200:where(.dark, .dark *) {
    color: var(--color-gray-200);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-200:not(:where(.light, .light *)) {
      color: var(--color-gray-200);
    }
  }

  .dark\:text-gray-300:where(.dark, .dark *) {
    color: var(--color-gray-300);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-300:not(:where(.light, .light *)) {
      color: var(--color-gray-300);
    }
  }

  .dark\:text-gray-400:where(.dark, .dark *) {
    color: var(--color-gray-400);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-400:not(:where(.light, .light *)) {
      color: var(--color-gray-400);
    }
  }

  .dark\:text-green-200:where(.dark, .dark *) {
    color: var(--color-green-200);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-200:not(:where(.light, .light *)) {
      color: var(--color-green-200);
    }
  }

  .dark\:text-green-300:where(.dark, .dark *) {
    color: var(--color-green-300);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-300:not(:where(.light, .light *)) {
      color: var(--color-green-300);
    }
  }

  .dark\:text-green-400:where(.dark, .dark *) {
    color: var(--color-green-400);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-400:not(:where(.light, .light *)) {
      color: var(--color-green-400);
    }
  }

  .dark\:text-orange-300:where(.dark, .dark *) {
    color: var(--color-orange-300);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-orange-300:not(:where(.light, .light *)) {
      color: var(--color-orange-300);
    }
  }

  .dark\:text-primary-muted:where(.dark, .dark *) {
    color: var(--primary-muted);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-primary-muted:not(:where(.light, .light *)) {
      color: var(--primary-muted);
    }
  }

  .dark\:text-red-200:where(.dark, .dark *) {
    color: var(--color-red-200);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-200:not(:where(.light, .light *)) {
      color: var(--color-red-200);
    }
  }

  .dark\:text-red-300:where(.dark, .dark *) {
    color: var(--color-red-300);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-300:not(:where(.light, .light *)) {
      color: var(--color-red-300);
    }
  }

  .dark\:text-red-400:where(.dark, .dark *) {
    color: var(--color-red-400);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-400:not(:where(.light, .light *)) {
      color: var(--color-red-400);
    }
  }

  .dark\:text-white:where(.dark, .dark *) {
    color: var(--color-white);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white:not(:where(.light, .light *)) {
      color: var(--color-white);
    }
  }

  .dark\:text-white\/40:where(.dark, .dark *) {
    color: #fff6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-white\/40:where(.dark, .dark *) {
      color: color-mix(in oklab, var(--color-white) 40%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/40:not(:where(.light, .light *)) {
      color: #fff6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/40:not(:where(.light, .light *)) {
        color: color-mix(in oklab, var(--color-white) 40%, transparent);
      }
    }
  }

  .dark\:text-white\/60:where(.dark, .dark *) {
    color: #fff9;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-white\/60:where(.dark, .dark *) {
      color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/60:not(:where(.light, .light *)) {
      color: #fff9;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/60:not(:where(.light, .light *)) {
        color: color-mix(in oklab, var(--color-white) 60%, transparent);
      }
    }
  }

  .dark\:text-white\/70:where(.dark, .dark *) {
    color: #ffffffb3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-white\/70:where(.dark, .dark *) {
      color: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/70:not(:where(.light, .light *)) {
      color: #ffffffb3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/70:not(:where(.light, .light *)) {
        color: color-mix(in oklab, var(--color-white) 70%, transparent);
      }
    }
  }

  .dark\:text-white\/80:where(.dark, .dark *) {
    color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-white\/80:where(.dark, .dark *) {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/80:not(:where(.light, .light *)) {
      color: #fffc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/80:not(:where(.light, .light *)) {
        color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }

  .dark\:text-white\/90:where(.dark, .dark *) {
    color: #ffffffe6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:text-white\/90:where(.dark, .dark *) {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/90:not(:where(.light, .light *)) {
      color: #ffffffe6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/90:not(:where(.light, .light *)) {
        color: color-mix(in oklab, var(--color-white) 90%, transparent);
      }
    }
  }

  .dark\:text-yellow-200:where(.dark, .dark *) {
    color: var(--color-yellow-200);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-200:not(:where(.light, .light *)) {
      color: var(--color-yellow-200);
    }
  }

  .dark\:text-yellow-300:where(.dark, .dark *) {
    color: var(--color-yellow-300);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-300:not(:where(.light, .light *)) {
      color: var(--color-yellow-300);
    }
  }

  .dark\:text-yellow-400:where(.dark, .dark *) {
    color: var(--color-yellow-400);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-400:not(:where(.light, .light *)) {
      color: var(--color-yellow-400);
    }
  }

  .dark\:text-zinc-50:where(.dark, .dark *) {
    color: var(--color-zinc-50);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-zinc-50:not(:where(.light, .light *)) {
      color: var(--color-zinc-50);
    }
  }

  .dark\:text-zinc-100:where(.dark, .dark *) {
    color: var(--color-zinc-100);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-zinc-100:not(:where(.light, .light *)) {
      color: var(--color-zinc-100);
    }
  }

  .dark\:text-zinc-200:where(.dark, .dark *) {
    color: var(--color-zinc-200);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-zinc-200:not(:where(.light, .light *)) {
      color: var(--color-zinc-200);
    }
  }

  .dark\:text-zinc-300:where(.dark, .dark *) {
    color: var(--color-zinc-300);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-zinc-300:not(:where(.light, .light *)) {
      color: var(--color-zinc-300);
    }
  }

  .dark\:text-zinc-400:where(.dark, .dark *) {
    color: var(--color-zinc-400);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-zinc-400:not(:where(.light, .light *)) {
      color: var(--color-zinc-400);
    }
  }

  .dark\:text-zinc-500:where(.dark, .dark *) {
    color: var(--color-zinc-500);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-zinc-500:not(:where(.light, .light *)) {
      color: var(--color-zinc-500);
    }
  }

  .dark\:\[color-scheme\:dark\]:where(.dark, .dark *) {
    color-scheme: dark;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:\[color-scheme\:dark\]:not(:where(.light, .light *)) {
      color-scheme: dark;
    }
  }

  .dark\:opacity-30:where(.dark, .dark *) {
    opacity: .3;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:opacity-30:not(:where(.light, .light *)) {
      opacity: .3;
    }
  }

  .dark\:shadow-\[0_-1px_0_1px_rgba\(0\,0\,0\,0\.8\)_inset\,0_0_0_1px_rgb\(9_9_11\)_inset\,0_0\.5px_0_1\.5px_\#71717a_inset\]:where(.dark, .dark *) {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #000c) inset, 0 0 0 1px var(--tw-shadow-color, #09090b) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #71717a) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:shadow-\[0_-1px_0_1px_rgba\(0\,0\,0\,0\.8\)_inset\,0_0_0_1px_rgb\(9_9_11\)_inset\,0_0\.5px_0_1\.5px_\#71717a_inset\]:not(:where(.light, .light *)) {
      --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #000c) inset, 0 0 0 1px var(--tw-shadow-color, #09090b) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #71717a) inset;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .dark\:shadow-\[0_-1px_0_1px_rgba\(0\,0\,0\,0\.8\)_inset\,0_0_0_1px_rgba\(34\,197\,94\,0\.3\)_inset\,0_0\.5px_0_1\.5px_rgba\(34\,197\,94\,0\.2\)_inset\]:where(.dark, .dark *) {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #000c) inset, 0 0 0 1px var(--tw-shadow-color, #22c55e4d) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #22c55e33) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:shadow-\[0_-1px_0_1px_rgba\(0\,0\,0\,0\.8\)_inset\,0_0_0_1px_rgba\(34\,197\,94\,0\.3\)_inset\,0_0\.5px_0_1\.5px_rgba\(34\,197\,94\,0\.2\)_inset\]:not(:where(.light, .light *)) {
      --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #000c) inset, 0 0 0 1px var(--tw-shadow-color, #22c55e4d) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #22c55e33) inset;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .dark\:shadow-\[0_-1px_0_1px_rgba\(0\,0\,0\,0\.8\)_inset\,0_0_0_1px_rgba\(239\,68\,68\,0\.3\)_inset\,0_0\.5px_0_1\.5px_rgba\(239\,68\,68\,0\.2\)_inset\]:where(.dark, .dark *) {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #000c) inset, 0 0 0 1px var(--tw-shadow-color, #ef44444d) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #ef444433) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:shadow-\[0_-1px_0_1px_rgba\(0\,0\,0\,0\.8\)_inset\,0_0_0_1px_rgba\(239\,68\,68\,0\.3\)_inset\,0_0\.5px_0_1\.5px_rgba\(239\,68\,68\,0\.2\)_inset\]:not(:where(.light, .light *)) {
      --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #000c) inset, 0 0 0 1px var(--tw-shadow-color, #ef44444d) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #ef444433) inset;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .dark\:shadow-\[0_-1px_0_1px_rgba\(255\,255\,255\,0\.8\)_inset\,0_0_0_1px_rgb\(226_232_240\)_inset\,0_0\.5px_0_1\.5px_\#64748b_inset\]:where(.dark, .dark *) {
    --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #fffc) inset, 0 0 0 1px var(--tw-shadow-color, #e2e8f0) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #64748b) inset;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:shadow-\[0_-1px_0_1px_rgba\(255\,255\,255\,0\.8\)_inset\,0_0_0_1px_rgb\(226_232_240\)_inset\,0_0\.5px_0_1\.5px_\#64748b_inset\]:not(:where(.light, .light *)) {
      --tw-shadow: 0 -1px 0 1px var(--tw-shadow-color, #fffc) inset, 0 0 0 1px var(--tw-shadow-color, #e2e8f0) inset, 0 .5px 0 1.5px var(--tw-shadow-color, #64748b) inset;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .dark\:shadow-main-foreground\/80:where(.dark, .dark *) {
    --tw-shadow-color: var(--main-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:shadow-main-foreground\/80:where(.dark, .dark *) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--main-foreground) 80%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:shadow-main-foreground\/80:not(:where(.light, .light *)) {
      --tw-shadow-color: var(--main-foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:shadow-main-foreground\/80:not(:where(.light, .light *)) {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--main-foreground) 80%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  .dark\:shadow-white\/10:where(.dark, .dark *) {
    --tw-shadow-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:shadow-white\/10:where(.dark, .dark *) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-white) 10%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:shadow-white\/10:not(:where(.light, .light *)) {
      --tw-shadow-color: #ffffff1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:shadow-white\/10:not(:where(.light, .light *)) {
        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-white) 10%, transparent) var(--tw-shadow-alpha), transparent);
      }
    }
  }

  .dark\:ring-white\/20:where(.dark, .dark *) {
    --tw-ring-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:ring-white\/20:where(.dark, .dark *) {
      --tw-ring-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:ring-white\/20:not(:where(.light, .light *)) {
      --tw-ring-color: #fff3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:ring-white\/20:not(:where(.light, .light *)) {
        --tw-ring-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  .dark\:drop-shadow-\[0_2px_8px_rgba\(255\,255\,255\,0\.2\)\]:where(.dark, .dark *) {
    --tw-drop-shadow-size: drop-shadow(0 2px 8px var(--tw-drop-shadow-color, #fff3));
    --tw-drop-shadow: var(--tw-drop-shadow-size);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  @media (prefers-color-scheme: dark) {
    .dark\:drop-shadow-\[0_2px_8px_rgba\(255\,255\,255\,0\.2\)\]:not(:where(.light, .light *)) {
      --tw-drop-shadow-size: drop-shadow(0 2px 8px var(--tw-drop-shadow-color, #fff3));
      --tw-drop-shadow: var(--tw-drop-shadow-size);
      filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
    }
  }

  .dark\:drop-shadow-\[0_2px_8px_rgba\(255\,255\,255\,0\.4\)\]:where(.dark, .dark *) {
    --tw-drop-shadow-size: drop-shadow(0 2px 8px var(--tw-drop-shadow-color, #fff6));
    --tw-drop-shadow: var(--tw-drop-shadow-size);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  @media (prefers-color-scheme: dark) {
    .dark\:drop-shadow-\[0_2px_8px_rgba\(255\,255\,255\,0\.4\)\]:not(:where(.light, .light *)) {
      --tw-drop-shadow-size: drop-shadow(0 2px 8px var(--tw-drop-shadow-color, #fff6));
      --tw-drop-shadow: var(--tw-drop-shadow-size);
      filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
    }
  }

  @media (hover: hover) {
    .dark\:group-hover\:text-white:where(.dark, .dark *):is(:where(.group):hover *) {
      color: var(--color-white);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:group-hover\:text-white:not(:where(.light, .light *)):is(:where(.group):hover *) {
        color: var(--color-white);
      }
    }
  }

  .dark\:placeholder\:text-red-300:where(.dark, .dark *)::placeholder {
    color: var(--color-red-300);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:placeholder\:text-red-300:not(:where(.light, .light *))::placeholder {
      color: var(--color-red-300);
    }
  }

  .dark\:placeholder\:text-white\/60:where(.dark, .dark *)::placeholder {
    color: #fff9;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:placeholder\:text-white\/60:where(.dark, .dark *)::placeholder {
      color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:placeholder\:text-white\/60:not(:where(.light, .light *))::placeholder {
      color: #fff9;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:placeholder\:text-white\/60:not(:where(.light, .light *))::placeholder {
        color: color-mix(in oklab, var(--color-white) 60%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:border-blue-500:where(.dark, .dark *):hover {
      border-color: var(--color-blue-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:border-blue-500:not(:where(.light, .light *)):hover {
        border-color: var(--color-blue-500);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-gray-600:where(.dark, .dark *):hover {
      background-color: var(--color-gray-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-600:not(:where(.light, .light *)):hover {
        background-color: var(--color-gray-600);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-gray-700:where(.dark, .dark *):hover {
      background-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-700:not(:where(.light, .light *)):hover {
        background-color: var(--color-gray-700);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-main-foreground\/56:where(.dark, .dark *):hover {
      background-color: var(--main-foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-main-foreground\/56:where(.dark, .dark *):hover {
        background-color: color-mix(in oklab, var(--main-foreground) 56%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-main-foreground\/56:not(:where(.light, .light *)):hover {
        background-color: var(--main-foreground);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-main-foreground\/56:not(:where(.light, .light *)):hover {
          background-color: color-mix(in oklab, var(--main-foreground) 56%, transparent);
        }
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-white\/5:where(.dark, .dark *):hover {
      background-color: #ffffff0d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-white\/5:where(.dark, .dark *):hover {
        background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-white\/5:not(:where(.light, .light *)):hover {
        background-color: #ffffff0d;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-white\/5:not(:where(.light, .light *)):hover {
          background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
        }
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-white\/10:where(.dark, .dark *):hover {
      background-color: #ffffff1a;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-white\/10:where(.dark, .dark *):hover {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-white\/10:not(:where(.light, .light *)):hover {
        background-color: #ffffff1a;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-white\/10:not(:where(.light, .light *)):hover {
          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-white\/20:where(.dark, .dark *):hover {
      background-color: #fff3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-white\/20:where(.dark, .dark *):hover {
        background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-white\/20:not(:where(.light, .light *)):hover {
        background-color: #fff3;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-white\/20:not(:where(.light, .light *)):hover {
          background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
        }
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-zinc-700:where(.dark, .dark *):hover {
      background-color: var(--color-zinc-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-zinc-700:not(:where(.light, .light *)):hover {
        background-color: var(--color-zinc-700);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-blue-300:where(.dark, .dark *):hover {
      color: var(--color-blue-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-blue-300:not(:where(.light, .light *)):hover {
        color: var(--color-blue-300);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-gray-200:where(.dark, .dark *):hover {
      color: var(--color-gray-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-gray-200:not(:where(.light, .light *)):hover {
        color: var(--color-gray-200);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-red-200:where(.dark, .dark *):hover {
      color: var(--color-red-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-red-200:not(:where(.light, .light *)):hover {
        color: var(--color-red-200);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-red-300:where(.dark, .dark *):hover {
      color: var(--color-red-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-red-300:not(:where(.light, .light *)):hover {
        color: var(--color-red-300);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-white:where(.dark, .dark *):hover {
      color: var(--color-white);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-white:not(:where(.light, .light *)):hover {
        color: var(--color-white);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-white\/80:where(.dark, .dark *):hover {
      color: #fffc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:text-white\/80:where(.dark, .dark *):hover {
        color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-white\/80:not(:where(.light, .light *)):hover {
        color: #fffc;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:text-white\/80:not(:where(.light, .light *)):hover {
          color: color-mix(in oklab, var(--color-white) 80%, transparent);
        }
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:text-white\/90:where(.dark, .dark *):hover {
      color: #ffffffe6;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:text-white\/90:where(.dark, .dark *):hover {
        color: color-mix(in oklab, var(--color-white) 90%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-white\/90:not(:where(.light, .light *)):hover {
        color: #ffffffe6;
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:text-white\/90:not(:where(.light, .light *)):hover {
          color: color-mix(in oklab, var(--color-white) 90%, transparent);
        }
      }
    }
  }

  .dark\:disabled\:opacity-60:where(.dark, .dark *):disabled {
    opacity: .6;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:disabled\:opacity-60:not(:where(.light, .light *)):disabled {
      opacity: .6;
    }
  }

  .dark\:data-\[selected\]\:text-white\/80:where(.dark, .dark *)[data-selected] {
    color: #fffc;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[selected\]\:text-white\/80:where(.dark, .dark *)[data-selected] {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:data-\[selected\]\:text-white\/80:not(:where(.light, .light *))[data-selected] {
      color: #fffc;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:data-\[selected\]\:text-white\/80:not(:where(.light, .light *))[data-selected] {
        color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:size-4 svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\:-webkit-autofill\]\:\!bg-white\/50:-webkit-autofill {
    background-color: #ffffff80 !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:-webkit-autofill\]\:\!bg-white\/50:-webkit-autofill {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent) !important;
    }
  }

  .\[\&\:-webkit-autofill\]\:\!text-black\/90:-webkit-autofill {
    color: #000000e6 !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:-webkit-autofill\]\:\!text-black\/90:-webkit-autofill {
      color: color-mix(in oklab, var(--color-black) 90%, transparent) !important;
    }
  }

  .\[\&\:-webkit-autofill\]\:dark\:\!bg-transparent:-webkit-autofill:where(.dark, .dark *) {
    background-color: #0000 !important;
  }

  @media (prefers-color-scheme: dark) {
    .\[\&\:-webkit-autofill\]\:dark\:\!bg-transparent:-webkit-autofill:not(:where(.light, .light *)) {
      background-color: #0000 !important;
    }
  }

  .\[\&\:-webkit-autofill\]\:dark\:\!text-white\/90:-webkit-autofill:where(.dark, .dark *) {
    color: #ffffffe6 !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:-webkit-autofill\]\:dark\:\!text-white\/90:-webkit-autofill:where(.dark, .dark *) {
      color: color-mix(in oklab, var(--color-white) 90%, transparent) !important;
    }
  }

  @media (prefers-color-scheme: dark) {
    .\[\&\:-webkit-autofill\]\:dark\:\!text-white\/90:-webkit-autofill:not(:where(.light, .light *)) {
      color: #ffffffe6 !important;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .\[\&\:-webkit-autofill\]\:dark\:\!text-white\/90:-webkit-autofill:not(:where(.light, .light *)) {
        color: color-mix(in oklab, var(--color-white) 90%, transparent) !important;
      }
    }
  }

  .\[\&\:-webkit-autofill\:active\]\:\!bg-white\/50:-webkit-autofill:active {
    background-color: #ffffff80 !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:-webkit-autofill\:active\]\:\!bg-white\/50:-webkit-autofill:active {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent) !important;
    }
  }

  .\[\&\:-webkit-autofill\:active\]\:dark\:\!bg-transparent:-webkit-autofill:active:where(.dark, .dark *) {
    background-color: #0000 !important;
  }

  @media (prefers-color-scheme: dark) {
    .\[\&\:-webkit-autofill\:active\]\:dark\:\!bg-transparent:-webkit-autofill:active:not(:where(.light, .light *)) {
      background-color: #0000 !important;
    }
  }

  .\[\&\:-webkit-autofill\:focus\]\:\!bg-white\/50:-webkit-autofill:focus {
    background-color: #ffffff80 !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:-webkit-autofill\:focus\]\:\!bg-white\/50:-webkit-autofill:focus {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent) !important;
    }
  }

  .\[\&\:-webkit-autofill\:focus\]\:dark\:\!bg-transparent:-webkit-autofill:focus:where(.dark, .dark *) {
    background-color: #0000 !important;
  }

  @media (prefers-color-scheme: dark) {
    .\[\&\:-webkit-autofill\:focus\]\:dark\:\!bg-transparent:-webkit-autofill:focus:not(:where(.light, .light *)) {
      background-color: #0000 !important;
    }
  }

  .\[\&\:-webkit-autofill\:hover\]\:\!bg-white\/50:-webkit-autofill:hover {
    background-color: #ffffff80 !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:-webkit-autofill\:hover\]\:\!bg-white\/50:-webkit-autofill:hover {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent) !important;
    }
  }

  .\[\&\:-webkit-autofill\:hover\]\:dark\:\!bg-transparent:-webkit-autofill:hover:where(.dark, .dark *) {
    background-color: #0000 !important;
  }

  @media (prefers-color-scheme: dark) {
    .\[\&\:-webkit-autofill\:hover\]\:dark\:\!bg-transparent:-webkit-autofill:hover:not(:where(.light, .light *)) {
      background-color: #0000 !important;
    }
  }

  .\[\&\:\:-webkit-scrollbar\]\:hidden::-webkit-scrollbar {
    display: none;
  }

  .data-\[ending-style\]\:\[\&\:not\(\[data-limited\]\)\]\:\[transform\:translateY\(150\%\)\][data-ending-style]:not([data-limited]) {
    transform: translateY(150%);
  }

  .\[\&\>button\]\:hidden > button {
    display: none;
  }

  .\[\&\>span\:last-child\]\:truncate > span:last-child {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .\[\&\>svg\]\:size-4 > svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:shrink-0 > svg {
    flex-shrink: 0;
  }

  [data-side="left"][data-collapsible="offcanvas"] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
    right: calc(var(--spacing) * -2);
  }

  [data-side="left"][data-state="collapsed"] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
    cursor: e-resize;
  }

  [data-side="right"][data-collapsible="offcanvas"] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
    left: calc(var(--spacing) * -2);
  }

  [data-side="right"][data-state="collapsed"] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
    cursor: w-resize;
  }
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

:root {
  --squircle-bg: #3b82f6;
  --squircle-border: transparent;
  --squircle-border-width: 0;
  --squircle-radius: 20px;
}

.squircle-css {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  border-radius: calc(var(--squircle-border-radius, var(--squircle-radius)) * .6);
  position: relative;
}

.squircle-enhanced {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  border-radius: calc(var(--squircle-border-radius, var(--squircle-radius)) * .7);
  position: relative;
}

.squircle-enhanced:before {
  content: "";
  background: inherit;
  border-radius: calc(var(--squircle-border-radius, var(--squircle-radius)) * .8);
  z-index: -1;
  opacity: .3;
  position: absolute;
  inset: -1px;
}

.squircle-clip {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  clip-path: polygon(0% calc(var(--squircle-border-radius, var(--squircle-radius)) * 1.2), calc(var(--squircle-border-radius, var(--squircle-radius)) * .8) 0%, calc(100% - calc(var(--squircle-border-radius, var(--squircle-radius)) * .8)) 0%, 100% calc(var(--squircle-border-radius, var(--squircle-radius)) * 1.2), 100% calc(100% - calc(var(--squircle-border-radius, var(--squircle-radius)) * 1.2)), calc(100% - calc(var(--squircle-border-radius, var(--squircle-radius)) * .8)) 100%, calc(var(--squircle-border-radius, var(--squircle-radius)) * .8) 100%, 0% calc(100% - calc(var(--squircle-border-radius, var(--squircle-radius)) * 1.2)));
}

.squircle-svg {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M20,0 L80,0 Q100,0 100,20 L100,80 Q100,100 80,100 L20,100 Q0,100 0,80 L0,20 Q0,0 20,0 Z' fill='white'/%3E%3C/svg%3E") 0 0 / 100% 100%;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M20,0 L80,0 Q100,0 100,20 L100,80 Q100,100 80,100 L20,100 Q0,100 0,80 L0,20 Q0,0 20,0 Z' fill='white'/%3E%3C/svg%3E") 0 0 / 100% 100%;
}

.squircle-sm {
  --squircle-border-radius: 12px;
}

.squircle-md {
  --squircle-border-radius: 20px;
}

.squircle-lg {
  --squircle-border-radius: 32px;
}

.squircle-xl {
  --squircle-border-radius: 48px;
}

.squircle-blue {
  --squircle-background-color: #3b82f6;
}

.squircle-green {
  --squircle-background-color: #10b981;
}

.squircle-red {
  --squircle-background-color: #ef4444;
}

.squircle-purple {
  --squircle-background-color: #8b5cf6;
}

.squircle-yellow {
  --squircle-background-color: #f59e0b;
}

@media (max-width: 768px) {
  .squircle-responsive {
    --squircle-border-radius: 16px;
  }
}

@media (min-width: 769px) {
  .squircle-responsive {
    --squircle-border-radius: 24px;
  }
}

.squircle-animated {
  transition: all .3s;
}

.squircle-animated:hover {
  filter: brightness(1.1);
  transform: scale(1.05);
}

.squircle {
  background: var(--squircle-background-color, var(--squircle-bg));
  border: var(--squircle-border-width, 0) solid var(--squircle-border-color, var(--squircle-border));
  border-radius: calc(var(--squircle-border-radius, var(--squircle-radius)) * .65);
}

.root {
  isolation: isolate;
}

.hide-browser-scrollbar {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  height: 100vh !important;
  overflow: hidden !important;
}

.hide-browser-scrollbar::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

html.hide-browser-scrollbar, body.hide-browser-scrollbar {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  height: 100vh !important;
  overflow: hidden !important;
}

html.hide-browser-scrollbar::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

body.hide-browser-scrollbar::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.styled-browser-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-light) transparent;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  touch-action: pan-y;
  overscroll-behavior: contain;
}

.styled-browser-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.styled-browser-scrollbar::-webkit-scrollbar-track {
  background: none;
  border-radius: 3px;
}

.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-light);
  border-radius: 3px;
  min-height: 20px;
  transition: background .2s;
}

.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-light);
}

.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-light);
}

.styled-browser-scrollbar::-webkit-scrollbar-corner {
  background: none;
}

.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-light);
  border: 1px solid var(--scrollbar-thumb-border-light);
}

html.styled-browser-scrollbar, body.styled-browser-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-light) transparent;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  touch-action: pan-y;
  overscroll-behavior: contain;
}

html.styled-browser-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

body.styled-browser-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

html.styled-browser-scrollbar::-webkit-scrollbar-track {
  background: none;
  border-radius: 3px;
}

body.styled-browser-scrollbar::-webkit-scrollbar-track {
  background: none;
  border-radius: 3px;
}

html.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-light);
  border: 1px solid var(--scrollbar-thumb-border-light);
  border-radius: 3px;
  min-height: 20px;
  transition: background .2s;
}

body.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-light);
  border: 1px solid var(--scrollbar-thumb-border-light);
  border-radius: 3px;
  min-height: 20px;
  transition: background .2s;
}

html.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-light);
}

body.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-light);
}

html.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-light);
}

body.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-light);
}

html.styled-browser-scrollbar::-webkit-scrollbar-corner {
  background: none;
}

body.styled-browser-scrollbar::-webkit-scrollbar-corner {
  background: none;
}

@media (prefers-color-scheme: dark) {
  .styled-browser-scrollbar {
    scrollbar-color: var(--scrollbar-thumb-dark) transparent;
  }

  .styled-browser-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-dark);
  }

  .styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover-dark);
  }

  .styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
    background: var(--scrollbar-thumb-active-dark);
  }

  .styled-browser-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-gradient-dark);
    border: 1px solid var(--scrollbar-thumb-border-dark);
  }

  html.styled-browser-scrollbar, body.styled-browser-scrollbar {
    scrollbar-color: var(--scrollbar-thumb-dark) transparent;
  }

  html.styled-browser-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-gradient-dark);
    border: 1px solid var(--scrollbar-thumb-border-dark);
  }

  body.styled-browser-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-gradient-dark);
    border: 1px solid var(--scrollbar-thumb-border-dark);
  }

  html.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover-dark);
  }

  body.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover-dark);
  }

  html.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
    background: var(--scrollbar-thumb-active-dark);
  }

  body.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
    background: var(--scrollbar-thumb-active-dark);
  }
}

.dark .styled-browser-scrollbar {
  scrollbar-color: var(--scrollbar-thumb-dark) transparent;
}

.dark .styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-dark);
}

.dark .styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-dark);
}

.dark .styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-dark);
}

.dark .styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-dark);
  border: 1px solid var(--scrollbar-thumb-border-dark);
}

.dark html.styled-browser-scrollbar, .dark body.styled-browser-scrollbar {
  scrollbar-color: var(--scrollbar-thumb-dark) transparent;
}

.dark html.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-dark);
  border: 1px solid var(--scrollbar-thumb-border-dark);
}

.dark body.styled-browser-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-dark);
  border: 1px solid var(--scrollbar-thumb-border-dark);
}

.dark html.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-dark);
}

.dark body.styled-browser-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-dark);
}

.dark html.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-dark);
}

.dark body.styled-browser-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-dark);
}

@media (max-width: 768px) {
  .styled-browser-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
    overscroll-behavior: contain;
    -webkit-scroll-behavior: smooth;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    transform: translateZ(0);
    overflow: hidden auto !important;
  }

  .styled-browser-scrollbar::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }

  html.styled-browser-scrollbar::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }

  body.styled-browser-scrollbar::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }

  html.styled-browser-scrollbar, body.styled-browser-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
    overscroll-behavior: contain;
    -webkit-scroll-behavior: smooth;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    transform: translateZ(0);
    overflow: hidden auto !important;
  }
}

:root {
  --main: oklch(.97 0 0);
  --main-secondary: oklch(97% 0 0);
  --main-muted: oklch(.96 0 0);
  --primary: oklch(0 0 0);
  --primary-foreground: oklch(37.1% 0 0);
  --primary-muted: oklch(.3 0 0);
  --border: oklch(.885 0 0);
}

.dark {
  --main: oklch(.178 0 0);
  --main-secondary: oklch(.205 0 0);
  --main-muted: oklch(.168 0 0);
  --primary: oklch(1 0 0);
  --primary-foreground: oklch(.97 0 0);
  --primary-muted: oklch(.85 0 0);
  --border: oklch(.26 0 0);
}

@keyframes spotlight {
  0% {
    opacity: 0;
    transform: translate(-72%, -62%)scale(.5);
  }

  100% {
    opacity: 1;
    transform: translate(-50%, -40%)scale(1);
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0;
    background-size: 200% 200%;
  }

  50% {
    background-position: 100%;
    background-size: 200% 200%;
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: 3s infinite gradient-x;
}

:root {
  --scrollbar-thumb-light: #00000026;
  --scrollbar-thumb-hover-light: #00000040;
  --scrollbar-thumb-active-light: #00000059;
  --scrollbar-thumb-gradient-light: linear-gradient(180deg, #0000001a 0%, #0003 100%);
  --scrollbar-thumb-border-light: #0000000d;
  --scrollbar-thumb-dark: #ffffff26;
  --scrollbar-thumb-hover-dark: #ffffff40;
  --scrollbar-thumb-active-dark: #ffffff59;
  --scrollbar-thumb-gradient-dark: linear-gradient(180deg, #ffffff1a 0%, #fff3 100%);
  --scrollbar-thumb-border-dark: #ffffff0d;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-light) transparent;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  touch-action: pan-y;
  overscroll-behavior: contain;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: none;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-light);
  border-radius: 3px;
  min-height: 20px;
  transition: background .2s;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-light);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-light);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: none;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-light);
  border: 1px solid var(--scrollbar-thumb-border-light);
}

@media (prefers-color-scheme: dark) {
  .custom-scrollbar {
    scrollbar-color: var(--scrollbar-thumb-dark) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-dark);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover-dark);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:active {
    background: var(--scrollbar-thumb-active-dark);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-gradient-dark);
    border: 1px solid var(--scrollbar-thumb-border-dark);
  }
}

.dark .custom-scrollbar {
  scrollbar-color: var(--scrollbar-thumb-dark) transparent;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-dark);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-dark);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-active-dark);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-gradient-dark);
  border: 1px solid var(--scrollbar-thumb-border-dark);
}

@media (max-width: 768px) {
  .custom-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
    overscroll-behavior: contain;
    -webkit-scroll-behavior: smooth;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    transform: translateZ(0);
    overflow: hidden auto !important;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }

  [data-flyout-panel="true"] .custom-scrollbar, .flyout-content .custom-scrollbar {
    will-change: scroll-position;
    z-index: 1;
    backface-visibility: hidden;
    perspective: 1000px;
    transform: translate3d(0, 0, 0);
    height: 100%;
    max-height: 100%;
    position: relative;
    -webkit-transform: translateZ(0);
  }

  [data-flyout-panel="true"] {
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
  }

  [data-flyout-panel="true"] .custom-scrollbar, .flyout-content .custom-scrollbar {
    isolation: isolate;
    flex: 1;
    min-height: 0;
    touch-action: pan-y !important;
    -webkit-overflow-scrolling: touch !important;
    overflow: hidden auto !important;
  }

  button, [role="button"], [data-radix-collection-item] {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    min-width: 44px;
    min-height: 44px;
  }

  [data-radix-tabs-trigger], [role="switch"], .toggle-switch {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    touch-action: manipulation;
    cursor: pointer;
  }
}

.squircle {
  background: paint(squircle);
  --squircle-border-width: 0;
  --squircle-border-radius: 12px;
  --squircle-border-smoothing: .6;
  background-repeat: no-repeat;
}

.squircle-mask {
  -webkit-mask-image: paint(squircle);
  -webkit-mask-image: paint(squircle);
  mask-image: paint(squircle);
  --squircle-mode: mask-image;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}

@supports not (background: paint(squircle)) {
  .squircle-xs {
    border-radius: 4px;
  }

  .squircle-sm {
    border-radius: 6px;
  }

  .squircle-md {
    border-radius: 8px;
  }

  .squircle-lg {
    border-radius: 12px;
  }

  .squircle-xl {
    border-radius: 16px;
  }

  .squircle-2xl {
    border-radius: 20px;
  }

  .squircle-3xl {
    border-radius: 24px;
  }
}

.squircle-mask {
  -webkit-mask-image: paint(squircle);
  -webkit-mask-image: paint(squircle);
  mask-image: paint(squircle);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}

.squircle, .squircle-mask {
  --squircle-border-width: 0;
  --squircle-border-color: transparent;
}

.squircle-active {
  --squircle-border-width: 1.5px;
  --squircle-border-color: #3e3e44;
}

.squircle {
  transition: all .2s ease-in-out;
}

.squircle:hover {
  --squircle-background-color: #2a2a2e;
}

.squircle {
  background: paint(squircle);
  --squircle-border-width: 0;
  --squircle-border-radius: 12px;
  --squircle-border-smoothing: .8;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

.scale-102 {
  transform: scale(1.02);
}

.scale-105 {
  transform: scale(1.05);
}

.transition-smooth {
  transition: all .6s;
}

.glass-morphism {
  -webkit-backdrop-filter: blur(24px) saturate(180%);
  background: linear-gradient(135deg, #ffffff59 0%, #fff3 30%, #ffffff26 70%, #ffffff14 100%);
  border: 1px solid #ffffff80;
  box-shadow: inset 0 1px 1px #fff9, inset 0 -1px 1px #fff3, 0 12px 40px #00000026, 0 6px 20px #0000001a, 0 2px 8px #00000014, 0 0 30px #ffffff14;
}

.icon-button-hover {
  transition: all .4s;
}

.icon-button-hover:hover:not(.active) {
  background: #ffffff14;
  transform: translateY(-1px)scale(1.02);
  box-shadow: 0 4px 12px #0000001a;
}

@keyframes gentle-pulse {
  0%, 100% {
    box-shadow: inset 0 1px 1px #fff9, inset 0 -1px 1px #fff3, 0 12px 40px #00000026, 0 0 30px #ffffff14;
  }

  50% {
    box-shadow: inset 0 1px 1px #ffffffb3, inset 0 -1px 1px #ffffff4d, 0 16px 48px #0000002e, 0 0 36px #ffffff1f;
  }
}

.icon-active-animation {
  animation: 3s ease-in-out infinite gentle-pulse;
}

@supports not ((-webkit-backdrop-filter: blur(1px)) or (backdrop-filter: blur(1px))) {
  .glass-morphism {
    background: linear-gradient(135deg, #fff6 0%, #ffffff40 30%, #fff3 70%, #ffffff26 100%);
  }
}

.squircle-enhanced {
  --squircle-border-smoothing: .85;
  transform-style: preserve-3d;
  will-change: transform, opacity;
}

.indicator-dot {
  filter: drop-shadow(0 0 6px #fffc);
  background: radial-gradient(circle, #fff 0%, #fffffff2 50%, #fffc 100%);
}

.sidebar-glass {
  -webkit-backdrop-filter: blur(20px) saturate(120%);
  background-color: #0000;
  background-image: linear-gradient(#141417f2 0%, #141417fa 50%, #141417f2 100%), radial-gradient(circle at 20% 20%, #ffffff05 0%, #0000 50%);
  background-position: 0 0;
  background-repeat: repeat;
  background-size: auto;
  background-attachment: scroll;
  background-origin: padding-box;
  background-clip: border-box;
  border-right: 1px solid #ffffff14;
}

@layer demo {
  .noise {
    --lines: .0003px;
    -webkit-mask: repeating-radial-gradient(circle at center, #000, var(--lines), #000, 0, #0000, calc(var(--lines) * 2), #0000 0);
    -webkit-mask: repeating-radial-gradient(circle at center, #000, var(--lines), #000, 0, #0000, calc(var(--lines) * 2), #0000 0);
    mask: repeating-radial-gradient(circle at center, #000, var(--lines), #000, 0, #0000, calc(var(--lines) * 2), #0000 0);
    --space: ;
    background: linear-gradient(to bottom right var(--space), var(--primary), var(--primary-muted)) fixed;
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    display: grid;
  }

  @supports (background: linear-gradient(in oklch, #000, #000)) {
    .noise {
      --space: in oklch;
    }
  }
}

.mobile-sheet-content {
  transition: transform .3s cubic-bezier(.25, .46, .45, .94) !important;
  animation: none !important;
  transform: translateX(-100%) !important;
}

@keyframes mobile-flyout-in {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes mobile-flyout-out {
  from {
    opacity: 1;
    transform: translateX(0);
  }

  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

.mobile-sheet-content {
  z-index: 10 !important;
  transition: transform .3s cubic-bezier(.25, .46, .45, .94) !important;
  animation: none !important;
  transform: translateX(-100%) !important;
}

.mobile-sheet-content[data-state="open"] {
  transform: translateX(0) !important;
}

.mobile-sheet-content[data-state="closed"] {
  transform: translateX(-100%) !important;
}

.mobile-sheet-content * {
  transition-property: none !important;
}

.sidebar-glass {
  backface-visibility: hidden;
  transform: translateZ(0);
  z-index: 30 !important;
}

.sidebar-glass .group, .sidebar-glass button {
  z-index: inherit;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.sidebar-glass button {
  will-change: transform;
  transition: transform .5s ease-out !important;
}

@media (max-width: 768px) {
  .sidebar-glass {
    z-index: 100 !important;
    position: fixed !important;
    transform: none !important;
  }

  .mobile-sheet-content {
    z-index: 50 !important;
  }
}

[data-sonner-toaster], [data-sonner-toast] {
  z-index: 2147483647 !important;
}

[data-sonner-toaster][data-theme="dark"] {
  --normal-bg: transparent !important;
  --normal-border: transparent !important;
  --normal-text: white !important;
}

.group\/toast {
  z-index: 2147483647 !important;
  position: fixed !important;
}

.group\/toast * {
  z-index: inherit !important;
}

:root {
  --primary: oklch(0 0 0);
  --primary-muted: oklch(.3 0 0);
  --primary-invert: oklch(1 0 0);
}

.dark {
  --primary: oklch(1 0 0);
  --primary-invert: oklch(0 0 0);
  --primary-muted: oklch(.85 0 0);
}

:root {
  --main: oklch(.97 0 0);
  --main-secondary: oklch(97% 0 0);
  --main-foreground: oklch(.925 0 0);
  --main-muted: oklch(.96 0 0);
  --main-background: oklch(.97 0 0);
  --main-invert: oklch(.205 0 0);
  --primary: oklch(0 0 0);
  --primary-foreground: oklch(37.1% 0 0);
  --primary-muted: oklch(.3 0 0);
  --primary-invert: oklch(1 0 0);
  --border: oklch(.885 0 0);
}

.dark {
  --main: oklch(.178 0 0);
  --main-secondary: oklch(.205 0 0);
  --main-foreground: oklch(.269 0 0);
  --main-muted: oklch(.168 0 0);
  --main-background: oklch(.145 0 0);
  --main-invert: oklch(.8 0 0);
  --primary: oklch(1 0 0);
  --primary-invert: oklch(0 0 0);
  --primary-foreground: oklch(.97 0 0);
  --primary-muted: oklch(.85 0 0);
  --border: oklch(.26 0 0);
}

@keyframes fastPulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .4;
  }
}

.fast-pulse {
  animation: .6s ease-in-out infinite fastPulse;
}

@keyframes rotate {
  to {
    --angle: 360deg;
  }
}

@property --angle {
  syntax: "<angle>";
  inherits: false;
  initial-value: 0deg;
}

input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.box_dark {
  background: linear-gradient(#000, #000) padding-box, linear-gradient(var(--angle), #070707, #e47320) border-box;
  border: 2px solid #0000;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: auto;
  height: auto;
  padding: 0;
  animation: 8s linear infinite rotate;
  display: flex;
}

.box_light {
  background: linear-gradient(#fff, #fff) padding-box, linear-gradient(var(--angle), #f2f2f2, #e47320) border-box;
  border: 2px solid #0000;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: auto;
  height: auto;
  padding: 0;
  animation: 8s linear infinite rotate;
  display: flex;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

@keyframes shine {
  from {
    background-position: 0 0;
  }

  to {
    background-position: -400% 0;
  }
}
